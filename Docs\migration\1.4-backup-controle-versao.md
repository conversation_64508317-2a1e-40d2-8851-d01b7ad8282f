# 1.4 Backup e Controle de Versão

## Objetivo
Criar backup completo do projeto e estabelecer controle de versão adequado antes de iniciar a migração.

## Pré-requisitos
- Acesso ao repositório Git
- Permissões de escrita no sistema de arquivos
- Git instalado e configurado

## Tempo Estimado
2-4 horas

## Passos Detalhados

### 1. Verificação do Estado Atual

```bash
# Navegar para o diretório do projeto
cd "d:\Visionnaire-RepGit\Visionnaire-WebPublication"

# Verificar status do Git
git status
git log --oneline -10
git branch -a

# Verificar se há mudanças não commitadas
git diff --name-only
```

### 2. Backup Físico Completo

```bash
# Criar diretório de backup
mkdir "d:\Backup\WebPublication-Java8-Backup"

# Backup usando robocopy (Windows)
robocopy "d:\Visionnaire-RepGit\Visionnaire-WebPublication" "d:\Backup\WebPublication-Java8-Backup" /E /COPYALL /R:3 /W:5

# Verificar integridade do backup
dir "d:\Backup\WebPublication-Java8-Backup" /s
```

### 3. Documentação do Estado Atual

Criar arquivo `MIGRATION_BASELINE.md`:

```markdown
# Baseline Java 8 - WebPublication

## Informações do Sistema
- **Data do Backup**: [DATA_ATUAL]
- **Versão Java**: 8 (jdk8u362-b09)
- **Tomcat**: 8.0.53
- **Servlet API**: 2.4
- **Build Tool**: Apache Ant

## Estrutura do Projeto
- Source: WEB-INF/src/
- Libraries: WEB-INF/lib/ (95+ JARs)
- Resources: WEB-INF/classes/
- Web Content: security/, restricted/, tools/

## Configurações Principais
- web.xml: Servlet 2.4
- context.xml: Múltiplos ambientes
- build.xml: Ant build configuration
- ant.properties: Build properties

## Bancos de Dados Configurados
- Oracle: CRM Production
- MySQL: Visionnaire sites
- SQL Server: FIEPE environments
- PostgreSQL: CITS production

## Dependências Críticas Identificadas
- PSS Framework (Visionnaire proprietário)
- Apache Commons (múltiplas versões)
- Jackson 2.6.x
- Log4j 1.x
- Elasticsearch 2.4.4
- DWR (Direct Web Remoting)
- Apache Tiles
- iText PDF
- JAI (Java Advanced Imaging)

## Próximos Passos
1. Inventário completo de dependências
2. Análise de compatibilidade Java 21
3. Plano de atualização de bibliotecas
```

### 4. Criação da Branch de Migração

```bash
# Garantir que está na branch principal
git checkout main

# Criar nova branch para migração
git checkout -b feature/java21-migration

# Adicionar arquivo de baseline
git add MIGRATION_BASELINE.md
git commit -m "docs: Add Java 8 baseline documentation for migration"

# Push da nova branch
git push -u origin feature/java21-migration
```

### 5. Configuração de Proteção da Branch

```bash
# Criar tag do estado atual
git tag -a "java8-baseline" -m "Java 8 baseline before migration to Java 21"
git push origin java8-baseline
```

### 6. Backup do Banco de Dados (Desenvolvimento)

Para cada ambiente configurado em `context.xml`:

#### Oracle (CRM Dev)
```sql
-- Conectar como WEBP_CRM_DEV
expdp WEBP_CRM_DEV/mSKqzKHzdDJk@**************:1521/MORALPDB \
  directory=BACKUP_DIR \
  dumpfile=webp_crm_dev_java8_baseline.dmp \
  logfile=webp_crm_dev_java8_baseline.log
```

#### MySQL (Visionnaire Dev)
```bash
mysqldump -h ************** -u root -pa4DqYcBvKB \
  dev_visionnaire > webp_visionnaire_dev_java8_baseline.sql
```

### 7. Documentação de Rollback

Criar arquivo `ROLLBACK_PLAN.md`:

```markdown
# Plano de Rollback - Java 21 Migration

## Cenários de Rollback

### 1. Rollback de Código
```bash
# Voltar para a branch principal
git checkout main

# Ou voltar para tag específica
git checkout java8-baseline

# Ou reverter commits específicos
git revert <commit-hash>
```

### 2. Rollback de Ambiente
- Reinstalar JDK 8
- Restaurar Tomcat 8.0.53
- Restaurar bibliotecas originais

### 3. Rollback de Banco
- Restaurar dumps criados
- Verificar integridade dos dados

## Critérios para Rollback
- Falhas críticas de compilação
- Perda de funcionalidade essencial
- Problemas de performance > 50%
- Instabilidade do sistema
```

### 8. Verificações Finais

#### Checklist de Backup
- [ ] Backup físico completo realizado
- [ ] Branch de migração criada
- [ ] Tag de baseline criada
- [ ] Documentação de baseline criada
- [ ] Plano de rollback documentado
- [ ] Backup de banco realizado (se aplicável)
- [ ] Equipe notificada sobre início da migração

#### Validação do Backup
```bash
# Verificar tamanho dos diretórios
du -sh "d:\Visionnaire-RepGit\Visionnaire-WebPublication"
du -sh "d:\Backup\WebPublication-Java8-Baseline"

# Verificar arquivos críticos
ls -la "d:\Backup\WebPublication-Java8-Baseline\Source\build.xml"
ls -la "d:\Backup\WebPublication-Java8-Baseline\Source\WEB-INF\web.xml"
```

## Troubleshooting

### Problema: Git não está inicializado
```bash
git init
git remote add origin [URL_DO_REPOSITORIO]
```

### Problema: Espaço insuficiente para backup
- Limpar arquivos temporários
- Usar compressão: `tar -czf backup.tar.gz projeto/`
- Backup incremental

### Problema: Permissões de arquivo
```bash
# Windows
icacls "d:\Backup" /grant Everyone:F /T

# Verificar permissões
icacls "d:\Backup\WebPublication-Java8-Baseline"
```

## Próximo Passo
Após completar o backup e controle de versão, prosseguir para:
**[1.1 Inventário de Dependências](./1.1-inventario-dependencias.md)**

---
**Status**: ⏳ Pendente
**Responsável**: DevOps/Tech Lead
**Estimativa**: 2-4 horas
