# Troubleshooting - Migração Java 8 para Java 21

## Problemas Comuns e Soluções

---

## Problemas de Ambiente

### Java não reconhecido após instalação

**Sintomas:**
```
'java' is not recognized as an internal or external command
```

**Soluções:**
```bash
# 1. Verificar JAVA_HOME
echo %JAVA_HOME%

# 2. Verificar PATH
echo %PATH% | findstr Java

# 3. Reabrir Command Prompt
# 4. Reiniciar sistema se necessário

# 5. Configurar manualmente
setx JAVA_HOME "C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot" /M
setx PATH "%JAVA_HOME%\bin;%PATH%" /M
```

### Múltiplas versões Java conflitando

**Sintomas:**
```
java -version
# Mostra Java 8 mesmo após instalar Java 21
```

**Soluções:**
```bash
# 1. Verificar todas as instalações
where java
where javac

# 2. Limpar PATH de versões antigas
# Via System Properties > Environment Variables

# 3. Script de alternância
# Usar switch_java.bat criado na documentação
```

### Tomcat não inicia com Java 21

**Sintomas:**
```
Error: Could not create the Java Virtual Machine
```

**Soluções:**
```bash
# 1. Verificar compatibilidade Tomcat/Java
# Tomcat 8.x não suporta Java 21
# Usar Tomcat 10.1.x ou superior

# 2. Verificar CATALINA_OPTS
set CATALINA_OPTS=-Xmx1024m -XX:+UseG1GC

# 3. Verificar logs
tail -f logs/catalina.out
```

---

## Problemas de Compilação

### Erros de import javax vs jakarta

**Sintomas:**
```java
import javax.servlet.http.HttpServlet; // Não encontrado
```

**Soluções:**
```java
// Substituir imports
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

// Script de substituição em massa
find . -name "*.java" -exec sed -i 's/javax\.servlet/jakarta.servlet/g' {} \;
```

### ClassNotFoundException para bibliotecas

**Sintomas:**
```
java.lang.ClassNotFoundException: org.apache.commons.lang.StringUtils
```

**Soluções:**
```java
// 1. Verificar se biblioteca foi atualizada
// Commons Lang 2 → Commons Lang 3

// Antes
import org.apache.commons.lang.StringUtils;

// Depois  
import org.apache.commons.lang3.StringUtils;

// 2. Verificar se JAR está no classpath
// 3. Verificar versão da biblioteca
```

### Erros de compilação com Log4j

**Sintomas:**
```java
import org.apache.log4j.Logger; // Não encontrado
```

**Soluções:**
```java
// Migrar para Log4j 2
// Antes (Log4j 1)
import org.apache.log4j.Logger;
private static final Logger logger = Logger.getLogger(MyClass.class);

// Depois (Log4j 2)
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
private static final Logger logger = LogManager.getLogger(MyClass.class);

// Ou usar bridge de compatibilidade
// log4j-1.2-api-2.20.0.jar
```

---

## Problemas de Dependências

### Conflitos de versão

**Sintomas:**
```
java.lang.NoSuchMethodError: org.apache.commons.lang3.StringUtils.isBlank
```

**Soluções:**
```bash
# 1. Verificar duplicatas
dir WEB-INF\lib | findstr commons-lang

# 2. Remover versões antigas
del WEB-INF\lib\commons-lang-2.6.jar

# 3. Manter apenas versão mais recente
# commons-lang3-3.12.0.jar

# 4. Verificar dependências transitivas
jdeps --class-path "WEB-INF\lib\*" WEB-INF\classes\
```

### Jackson versões incompatíveis

**Sintomas:**
```
com.fasterxml.jackson.databind.JsonMappingException
```

**Soluções:**
```bash
# 1. Atualizar todas as bibliotecas Jackson juntas
# jackson-core-2.15.2.jar
# jackson-databind-2.15.2.jar  
# jackson-annotations-2.15.2.jar

# 2. Verificar compatibilidade de versões
# Todas devem ter a mesma versão major.minor

# 3. Limpar cache se usando Maven/Gradle
```

### PSS Framework incompatível

**Sintomas:**
```
java.lang.UnsupportedClassVersionError: com/visionnaire/PSS/...
```

**Soluções:**
```bash
# 1. Contatar equipe Visionnaire
# Solicitar versão compatível com Java 21

# 2. Verificar se existe versão mais recente
# pss.jar, acss-local.jar, etc.

# 3. Considerar alternativas temporárias
# Usar reflection para compatibilidade
```

---

## Problemas de Runtime

### Module System conflicts

**Sintomas:**
```
java.lang.IllegalAccessError: class X cannot access class Y
```

**Soluções:**
```bash
# 1. Adicionar JVM arguments
--add-opens java.base/java.lang=ALL-UNNAMED
--add-opens java.base/java.util=ALL-UNNAMED

# 2. No Tomcat (setenv.bat)
set CATALINA_OPTS=%CATALINA_OPTS% --add-opens java.base/java.lang=ALL-UNNAMED

# 3. Atualizar código para não usar reflection em APIs internas
```

### Reflection warnings

**Sintomas:**
```
WARNING: Illegal reflective access by org.apache.commons.beanutils.PropertyUtils
```

**Soluções:**
```bash
# 1. Atualizar bibliotecas para versões compatíveis
# 2. Adicionar --add-opens conforme necessário
# 3. Suprimir warnings temporariamente
--add-opens java.base/java.lang=ALL-UNNAMED
```

### Performance degradation

**Sintomas:**
- Aplicação mais lenta que Java 8
- Alto uso de CPU
- Garbage Collection frequente

**Soluções:**
```bash
# 1. Ajustar GC settings
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:+UseStringDeduplication

# 2. Ajustar heap size
-Xms2g -Xmx4g

# 3. Monitorar com JProfiler/VisualVM
# 4. Verificar se há memory leaks
```

---

## Problemas de Banco de Dados

### Driver JDBC incompatível

**Sintomas:**
```
java.sql.SQLException: No suitable driver found
```

**Soluções:**
```bash
# 1. Atualizar drivers para versões Java 21
# MySQL: mysql-connector-java-8.0.33.jar
# Oracle: ojdbc11-21.x.jar
# SQL Server: mssql-jdbc-12.x.jar
# PostgreSQL: postgresql-42.x.jar

# 2. Verificar se driver está no classpath
# 3. Verificar URL de conexão
```

### Connection pool issues

**Sintomas:**
```
org.apache.commons.dbcp.SQLNestedException: Cannot get a connection
```

**Soluções:**
```xml
<!-- Atualizar para DBCP2 -->
<Resource name="jdbc/WebPublication" 
          auth="Container" 
          type="javax.sql.DataSource"
          driverClassName="com.mysql.cj.jdbc.Driver"
          url="********************************************************************"
          username="user" 
          password="pass"
          maxTotal="20" 
          maxIdle="10"
          maxWaitMillis="10000"/>
```

---

## Problemas de Deploy

### WAR não deploya no Tomcat

**Sintomas:**
```
SEVERE: Error deploying web application archive
```

**Soluções:**
```bash
# 1. Verificar logs do Tomcat
tail -f logs/catalina.out

# 2. Verificar web.xml syntax
xmllint --noout Source\WEB-INF\web.xml

# 3. Verificar compatibilidade Servlet API
# Servlet 5.0 requer Tomcat 10+

# 4. Verificar permissões de arquivo
```

### Context.xml não carrega

**Sintomas:**
```
SEVERE: Error in ResourceLink
```

**Soluções:**
```xml
<!-- Verificar sintaxe do context.xml -->
<!-- Verificar se DataSource está configurado corretamente -->
<!-- Verificar se driver JDBC está disponível -->

<Context>
    <Resource name="jdbc/WebPublication" 
              auth="Container" 
              type="javax.sql.DataSource"
              ... />
</Context>
```

---

## Problemas de Performance

### Startup lento

**Sintomas:**
- Aplicação demora muito para iniciar
- Timeout durante deploy

**Soluções:**
```bash
# 1. Ajustar JVM options
-XX:+TieredCompilation
-XX:TieredStopAtLevel=1

# 2. Verificar inicialização de beans
# 3. Otimizar carregamento de recursos
# 4. Usar parallel GC durante startup
-XX:+UseParallelGC
```

### Memory leaks

**Sintomas:**
```
java.lang.OutOfMemoryError: Java heap space
```

**Soluções:**
```bash
# 1. Aumentar heap size temporariamente
-Xmx4g

# 2. Analisar com profiler
# 3. Verificar se há listeners não removidos
# 4. Verificar ThreadLocal usage
# 5. Verificar connection leaks
```

---

## Scripts de Diagnóstico

### check_environment.bat
```batch
@echo off
echo === JAVA ENVIRONMENT CHECK ===
echo.
echo Java Version:
java -version
echo.
echo Java Home:
echo %JAVA_HOME%
echo.
echo Classpath:
echo %CLASSPATH%
echo.
echo Tomcat Version:
if exist "%CATALINA_HOME%\bin\version.bat" (
    call "%CATALINA_HOME%\bin\version.bat"
) else (
    echo Tomcat not found
)
```

### check_dependencies.ps1
```powershell
# Verificar dependências problemáticas
$libPath = "Source\WEB-INF\lib"
$problematic = @("commons-lang-2", "jackson-databind-2.6", "log4j-1")

foreach ($lib in $problematic) {
    $found = Get-ChildItem "$libPath\$lib*.jar" -ErrorAction SilentlyContinue
    if ($found) {
        Write-Host "⚠️ Biblioteca problemática encontrada: $($found.Name)" -ForegroundColor Yellow
    }
}
```

---

## Contatos de Suporte

### Interno
- **Tech Lead**: [Nome/Email]
- **DevOps**: [Nome/Email]
- **Arquiteto**: [Nome/Email]

### Externo
- **Visionnaire PSS**: [Contato para framework proprietário]
- **Suporte Oracle**: [Para problemas com driver Oracle]
- **Comunidade**: Stack Overflow, GitHub Issues

---

## Logs Importantes

### Locais de Log
```
# Tomcat
logs/catalina.out
logs/localhost.log

# Aplicação
logs/webpublication.log

# Sistema
Windows Event Viewer
```

### Comandos Úteis
```bash
# Monitorar logs em tempo real
tail -f logs/catalina.out

# Buscar erros específicos
grep -i "error" logs/catalina.out
findstr /i "exception" logs\catalina.out

# Verificar uso de memória
jcmd <pid> VM.memory_summary
```

---

**Última Atualização**: [Data]
**Versão**: 1.0
