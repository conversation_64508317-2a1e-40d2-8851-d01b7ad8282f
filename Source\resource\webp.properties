####################################################
### WEBP.PROPERTIES - Vers�o 25.7.0 - 06/04/2017 ###
####################################################

# Configura��es do servi�o WEBP
# para webp.url, obrigat�rio / no final
webp.url=http://localhost:8080/webp/
webp.urlSafe=https://localhost:8080/webp/
webpservices_address=services/ServiceServer?wsdl
webp.email=<EMAIL>
webp.application.stage=desenvolvimento

# Configura��es (Texto dos campos) para envio de e-mail do workflow
webp.workflow.from=<EMAIL>
webp.workflow.text=Voc� recebeu um conte�do do WorkFlow do WebPublication para aprovar. <br>Por favor verifique suas pend�cias atrav�s do WebPublication. Utilize o link abaixo para isso:<br><a href="http://localhost:8080/webp">Clique aqui para iniciar a aplica��o</a> 
webp.workflow.subject=WorkFlow do WebPublication

# Configura��es do CARONTE
caronte.middleware=library

# Localiza��o dos arquivos de index para o search do Lucene
webp.search.indexFolder=C:\\temp\\index\\

# O atributo abaixo � utilizado para diferenciar c�digos espec�ficos da internet da intranet
# Use "intranet" ou "internet"

webp.site.type=internet
webp.intranet.url=
webp.intranet.root=
#webp.site.type=intranet
#webp.intranet.url=http://localhost:8080/uic/
#webp.intranet.root=C:\\Workspace\\WorkFIEP\\.metadata\\.plugins\\org.eclipse.wst.server.core\\tmp0\\wtpwebapps\\uic\\

# pid do site no qual utilizar� a pasta layout do site para pegar as imagens e css necess�rios para o envio do email.
# OBS: o e-mail � padr�o para o sistema, ou seja, � �nico, por isso se indicarmos apenas o pid do site que utiliza a estrutura, ja estar� OK
webp.emailtemplate.site.pid=1

# Indica qual pasta ser� usada como reposit�rio de backup do site selcionado
webp.backup.folder=c:\\temp\\backup\\

# Indica qual pasta ser� usada como raiz do reposit�rio de templates dos sites cadastrados
# para webp.repository.folder, obrigat�rio / no final
webp.repository.folder=C:\\Rep-Git\\CRMPR-AtualizacaoWebp\\Source\\webp_templates\\

#especifica a pasta dentro da raiz do repositorio de templates que contem os arquivos de widget
webp.repository.widget.folder=commonfiles\\widget
#especifica a pasta dentro da raiz do repositorio de templates que cont�m os arquivos e diret�rios que devem ser copiados para todos os sites
webp.repository.commonfiles.folder=commonfiles
#indica o nome do arquivo de widget que ser� gerado nos sites pais
webp.widget.file=webp_widget.shtml
#indica o nome do arquivo de widget que ser� gerado nos sites pais
webp.widget_all.file=webp_widget_all.shtml

# Indica qual o nome dos arquivos de estrutura
webp.struct.file=struct.xsl
webp.header.file=head.html
webp.footer.file=foot.html

# Indica qual a extens�o para arquivos din�micos e n�o din�micos do sistema
# tipo funcional: est�tico -> shtml ; din�mico -> asp ou php
# internet
webp.dynamic.type=php
webp.nodynamic.type=shtml
#intranet
#webp.dynamic.type=jsp
#webp.nodynamic.type=jsp

#Indica o numero de comentarios por pagina
webp.commentarypage=4

webp.commentary.captcha=true
#webp.commentary.captcha.privatekey=6LdhFeUSAAAAAHs9EzwmjQLQecs7rzsUS-OG8hBO 
webp.commentary.captcha.privatekey=6LfCOkIUAAAAANLGMkO1UQHPh2t93EX0Lcjl09le
#webp.commentary.captcha.publickey=6LdhFeUSAAAAAG9VH_42gz2k0wwtctPPylpj-tUr 
webp.commentary.captcha.publickey=6LfCOkIUAAAAAKV8T4wGpQEu9R-Hs2MSNlB9RFO5

# CRM-PR - Dados Captcha CRM-PR
# <EMAIL>
# crmpr2013

# Indica a base que esta sendo utilizada
# sqlserver, mysql, oracle ou postgres
#webp.dbname=mysql
webp.dbname=oracle

#Configura��o utilizada na indexa��o do elastic para conex�o no banco atual
#Elastic Search porta: 9300 / 9200
webp.update.elastic=NAO
#webp.database.name=WEBP_INTERNET
webp.database.host=**************
#webp.database.host=localhost
webp.database.port=1521
webp.database.user=WEBP_CRM_DEV
webp.database.pass=mSKqzKHzdDJk
webp.database.elastic.host=localhost
webp.database.elastic.port=9300
webp.database.elastic.bulk.execute=200

webp.database.elastic.executing.indexer=Existe uma Indexa��o em andamento no ElasticSearch!
webp.database.elastic.finalized.clean=A remo��o dos �ndices do ElasticSearch foi finalizada.
ebp.database.elastic.finalized.indexer=A Indexa��o do ElasticSearch foi finalizada.

#Indica o Cliente que esta usando o WEBP
# FIEP / UNIMED / INTRANET-VISIONNAIRE / APS / PREFEITURAPINHAIS / PIRAQUARA / CITS
webp.client=CRM-PR

#Parametros de busca
webp.busca.template=C:\\Rep-Git\\CRMPR-AtualizacaoWebp\\Source\\webp_templates\\portal-novo\\search_result.xsl
webp.busca.paginacao=20
webp.busca.template.sitePid=1

webp.crmpr.listagem.sites.medicos.template=C:\\Rep-Git\\CRMPR-AtualizacaoWebp\\Source\\webp_templates\\portal-novo\\listagem_sites_medicos.xsl
webp.crmpr.listagem.sites.medicos.site.principal.pid=1
webp.crmpr.listagem.sites.medicos.site.medico.pid=155

webp.crmpr.autenticacao.certificado.template=C:\\Rep-Git\\CRMPR-AtualizacaoWebp\\Source\\webp_templates\\portal-novo\\autenticar_certificado.xsl
webp.crmpr.autenticacao.certificado.site.pid=1
webp.crmpr.autenticacao.certificado.email=<EMAIL>

# Config CRM-PR
webp.crmpr.autenticacao.certificado.template=C:\\Rep-Git\\CRMPR-AtualizacaoWebp\\Source\\webp_templates\\portal-novo\\autenticar_certificado.xsl
webp.crmpr.autenticacao.certificado.site.pid=1
webp.crmpr.autenticacao.certificado.email=<EMAIL>
webp.pdf.certificado.email.template=C:\\Rep-Git\\CRMPR-AtualizacaoWebp\\Source\\webp_templates\\portal-novo\\email_certificado.xsl
webp.pdf.certificado.img_frente=http://localhost:82/crmpr-novo/img/img_frenteB.jpg
webp.pdf.certificado.img_verso=http://localhost:82/crmpr-novo/img/img_versoB.jpg

# Configura��es do RSS
webp.rss.client=CRM-PR
webp.rss.descricao=CRM-PR - Direitos Reservados
webp.rss.templateSitePid=1
webp.rss.maximoConteudoXML=100

# Utiliza��o de proxy para acesso externo. Casos seja SIM, deve preencher os campos abaixo, host e port
webp.proxy=NAO
#webp.proxy.host=************
#webp.proxy.port=8080

#Configura��es do Leitor de RSS
webp.rssreader.maxContentRSS=5

#Configura��es para gerar o question�rio (formul�rio com as quest�es) em PDF
webp.pdf.subject=CRM - PR
webp.pdf.keywords=http://localhost:82/crmpr/
webp.pdf.author=Visionnaire
webp.pdf.cabecalho=http://localhost:82/crmpr/img/logo_crmpr.jpg

webp.pdf.certificado.img_frente=http://localhost:82/crmpr/img/img_frenteB.jpg
webp.pdf.certificado.img_verso=http://localhost:82/crmpr/img/img_versoB.jpg

#Identifica se formul�rio poder� ser de incri��o
webp.formulario.inscricao=SIM

# Parametriza��o das frases e dos campos quando quer relacionar um formul�rio
webp.eventagenda.tied.research=true
webp.eventagenda.tied.research.model1.label=Relacionar um formul�rio M�dico PR ao evento?
webp.eventagenda.tied.research.model1.show=true
webp.eventagenda.tied.research.model2.label=#
webp.eventagenda.tied.research.model2.show=false
webp.eventagenda.tied.research.model3.label=#
webp.eventagenda.tied.research.model3.show=false
webp.eventagenda.tied.research.model4.label=#
webp.eventagenda.tied.research.model4.show=false

# Habilita a op��o de endere�o virtual
webp.component.visualcontent.virtualname=false

# caminho f�sico do arquivo config do servidor web (IIS - web.config ou Apache - .htaccess)
webp.servidorweb.config.path=

# Tempo para execu��o da task UserIntegrationSIEM
webp.task.userintegration.period.minute=5

# Tamanho de fonte para impress�o no certificados
webp.research.certified.name.font.size.muitopequeno=16
webp.research.certified.name.font.size.pequeno=22
webp.research.certified.name.font.size.normal=26
webp.research.certified.name.font.size.grande=30
webp.research.certified.name.font.size.muitogrande=36