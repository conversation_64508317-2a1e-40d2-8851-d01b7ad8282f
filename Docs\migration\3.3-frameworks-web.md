# 3.3 Frameworks Web

## Objetivo
Atualizar Servlet API, JSP API, JSTL, Tiles, DWR e DisplayTag para versões compatíveis com Java 21 e Jakarta EE.

## Pré-requisitos
- Bibliotecas core atualizadas (3.2)
- Tomcat 10.1.15 instalado
- JDK 21 configurado

## Tempo Estimado
1-2 semanas

## Frameworks Web Identificados

### **Frameworks Atuais na Aplicação:**

#### **1. Servlet API**
- **Versão Atual**: 2.4 (javax.servlet)
- **Localização**: web.xml, classes Java
- **Uso**: Extensivo em toda aplicação

#### **2. JSP API**
- **Versão Atual**: 2.0
- **Localização**: Páginas JSP, taglibs
- **Uso**: Interface web principal

#### **3. JSTL (JSP Standard Tag Library)**
- **Versão Atual**: 1.1
- **Localização**: Páginas JSP
- **Uso**: Tags de controle e formatação

#### **4. Apache Tiles**
- **Versão Atual**: 2.x
- **Localização**: tiles.xml, JSPs
- **Uso**: Layout e template engine

#### **5. DWR (Direct Web Remoting)**
- **Versão Atual**: 2.x
- **Localização**: dwr.xml, JavaScript
- **Uso**: AJAX e comunicação client-server

#### **6. DisplayTag**
- **Versão Atual**: 1.x
- **Localização**: JSPs, taglibs
- **Uso**: Tabelas e paginação

## Análise de Compatibilidade

### **1. Servlet API - CRÍTICO**

#### **Situação Atual:**
```xml
<!-- web.xml atual -->
<web-app version="2.4" 
    xmlns="http://java.sun.com/xml/ns/j2ee">
```

#### **Problemas:**
- Servlet 2.4 (2003) - Extremamente desatualizado
- Namespace javax.* - Incompatível com Tomcat 10+
- APIs removidas/depreciadas

#### **Migração Necessária:**
```xml
<!-- web.xml atualizado -->
<web-app version="5.0"
    xmlns="https://jakarta.ee/xml/ns/jakartaee">
```

#### **Mudanças de Código:**
```java
// ANTES (javax)
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.ServletException;

// DEPOIS (jakarta)
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.ServletException;
```

### **2. JSP API - CRÍTICO**

#### **Migração:**
```java
// ANTES
import javax.servlet.jsp.JspException;
import javax.servlet.jsp.PageContext;
import javax.servlet.jsp.tagext.TagSupport;

// DEPOIS
import jakarta.servlet.jsp.JspException;
import jakarta.servlet.jsp.PageContext;
import jakarta.servlet.jsp.tagext.TagSupport;
```

#### **Páginas JSP:**
```jsp
<%-- ANTES --%>
<%@ page import="javax.servlet.http.HttpSession" %>

<%-- DEPOIS --%>
<%@ page import="jakarta.servlet.http.HttpSession" %>
```

### **3. JSTL - ALTO RISCO**

#### **Versões e Compatibilidade:**
| JSTL Version | Servlet API | Java 21 | Status |
|--------------|-------------|---------|---------|
| 1.1 | 2.4 (javax) | ❌ | Incompatível |
| 1.2 | 2.5 (javax) | ❌ | Incompatível |
| 2.0 | 4.0 (javax) | ❌ | Incompatível |
| 3.0 | 5.0 (jakarta) | ✅ | **Compatível** |

#### **Atualização Necessária:**
```xml
<!-- Remover JSTL antigo -->
<!-- jstl-1.1.jar, standard-1.1.jar -->

<!-- Adicionar JSTL 3.0 -->
<!-- jakarta.servlet.jsp.jstl-3.0.1.jar -->
<!-- jakarta.servlet.jsp.jstl-api-3.0.0.jar -->
```

#### **Mudanças em JSP:**
```jsp
<%-- URIs permanecem iguais --%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>
```

### **4. Apache Tiles - MÉDIO RISCO**

#### **Versões e Compatibilidade:**
| Tiles Version | Servlet API | Java 21 | Status |
|---------------|-------------|---------|---------|
| 2.x | 2.4+ (javax) | ❌ | Incompatível |
| 3.0.8+ | 3.0+ (javax/jakarta) | ✅ | **Compatível** |

#### **Atualização Necessária:**
```xml
<!-- Remover Tiles 2.x -->
<!-- tiles-core-2.x.jar, tiles-jsp-2.x.jar -->

<!-- Adicionar Tiles 3.0.8 -->
<!-- tiles-core-3.0.8.jar -->
<!-- tiles-web-3.0.8.jar -->
<!-- tiles-servlet-3.0.8.jar -->
<!-- tiles-jsp-3.0.8.jar -->
<!-- tiles-el-3.0.8.jar -->
```

#### **Configuração Atualizada:**
```xml
<!-- tiles.xml - permanece compatível -->
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE tiles-definitions PUBLIC
    "-//Apache Software Foundation//DTD Tiles Configuration 3.0//EN"
    "http://tiles.apache.org/dtds/tiles-config_3_0.dtd">

<tiles-definitions>
    <definition name="base.definition" template="/WEB-INF/jsp/layout/base.jsp">
        <put-attribute name="title" value="WebPublication"/>
        <put-attribute name="header" value="/WEB-INF/jsp/layout/header.jsp"/>
        <put-attribute name="menu" value="/WEB-INF/jsp/layout/menu.jsp"/>
        <put-attribute name="body" value=""/>
        <put-attribute name="footer" value="/WEB-INF/jsp/layout/footer.jsp"/>
    </definition>
</tiles-definitions>
```

### **5. DWR (Direct Web Remoting) - ALTO RISCO**

#### **Situação Atual:**
- DWR 2.x - Muito antigo
- Última atualização: 2008
- Suporte limitado para Java moderno

#### **Opções de Migração:**

##### **Opção A: DWR 3.0 (Recomendado)**
```xml
<!-- Atualizar para DWR 3.0.2-RELEASE -->
<!-- dwr-3.0.2-RELEASE.jar -->
```

##### **Opção B: Migração para Tecnologia Moderna**
- **Spring WebMVC + AJAX**
- **RESTful APIs + JavaScript**
- **WebSockets** para comunicação real-time

#### **Configuração DWR 3.0:**
```xml
<!-- web.xml -->
<servlet>
    <servlet-name>dwr-invoker</servlet-name>
    <servlet-class>org.directwebremoting.servlet.DwrServlet</servlet-class>
    <init-param>
        <param-name>debug</param-name>
        <param-value>false</param-value>
    </init-param>
    <init-param>
        <param-name>crossDomainSessionSecurity</param-name>
        <param-value>false</param-value>
    </init-param>
    <init-param>
        <param-name>allowScriptTagRemoting</param-name>
        <param-value>true</param-value>
    </init-param>
    <load-on-startup>1</load-on-startup>
</servlet>

<servlet-mapping>
    <servlet-name>dwr-invoker</servlet-name>
    <url-pattern>/dwr/*</url-pattern>
</servlet-mapping>
```

### **6. DisplayTag - MÉDIO RISCO**

#### **Versões e Compatibilidade:**
| DisplayTag Version | Servlet API | Java 21 | Status |
|--------------------|-------------|---------|---------|
| 1.2 | 2.3+ (javax) | ❌ | Incompatível |
| 2.0+ | 3.0+ (javax) | ⚠️ | Parcialmente |

#### **Problemas Identificados:**
- Desenvolvimento descontinuado
- Dependências antigas
- Suporte limitado para Jakarta EE

#### **Alternativas Recomendadas:**
1. **Migração para DataTables.js**
2. **Bootstrap Tables**
3. **Custom JSP tags**

## Plano de Migração Detalhado

### **Fase 3.3.1: Servlet/JSP API (CRÍTICO)**

#### **Tempo**: 2-3 dias

```bash
# 1. Backup do código atual
mkdir "backup\servlet-javax"
xcopy "Source\WEB-INF\src" "backup\servlet-javax\src" /E /I

# 2. Script de migração automática
```

#### **Script de Migração:**
```powershell
# migrate_servlet_api.ps1
$sourceDir = "Source\WEB-INF\src"
$javaFiles = Get-ChildItem -Path $sourceDir -Recurse -Include "*.java"

Write-Host "Migrando $($javaFiles.Count) arquivos Java..."

foreach ($file in $javaFiles) {
    $content = Get-Content $file.FullName -Raw
    
    # Substituições javax → jakarta
    $content = $content -replace 'import javax\.servlet\.', 'import jakarta.servlet.'
    $content = $content -replace 'import javax\.servlet\.http\.', 'import jakarta.servlet.http.'
    $content = $content -replace 'import javax\.servlet\.jsp\.', 'import jakarta.servlet.jsp.'
    
    # Salvar arquivo atualizado
    $content | Out-File $file.FullName -Encoding UTF8
}

Write-Host "Migração de imports concluída!"
```

### **Fase 3.3.2: JSTL (ALTO RISCO)**

#### **Tempo**: 1-2 dias

```bash
# 1. Remover JSTL antigo
del "Source\WEB-INF\lib\jstl*.jar"
del "Source\WEB-INF\lib\standard*.jar"

# 2. Adicionar JSTL 3.0
copy "temp\new-libraries\jakarta.servlet.jsp.jstl-3.0.1.jar" "Source\WEB-INF\lib\"
copy "temp\new-libraries\jakarta.servlet.jsp.jstl-api-3.0.0.jar" "Source\WEB-INF\lib\"
```

### **Fase 3.3.3: Apache Tiles**

#### **Tempo**: 2-3 dias

```bash
# 1. Remover Tiles 2.x
del "Source\WEB-INF\lib\tiles-*-2.*.jar"

# 2. Adicionar Tiles 3.0.8
copy "temp\new-libraries\tiles-core-3.0.8.jar" "Source\WEB-INF\lib\"
copy "temp\new-libraries\tiles-web-3.0.8.jar" "Source\WEB-INF\lib\"
copy "temp\new-libraries\tiles-servlet-3.0.8.jar" "Source\WEB-INF\lib\"
copy "temp\new-libraries\tiles-jsp-3.0.8.jar" "Source\WEB-INF\lib\"
copy "temp\new-libraries\tiles-el-3.0.8.jar" "Source\WEB-INF\lib\"
```

#### **Atualizar web.xml:**
```xml
<!-- Context Parameters para Tiles 3.0 -->
<context-param>
    <param-name>org.apache.tiles.impl.BasicTilesContainer.DEFINITIONS_CONFIG</param-name>
    <param-value>/WEB-INF/tiles.xml</param-value>
</context-param>

<!-- Listener para Tiles 3.0 -->
<listener>
    <listener-class>org.apache.tiles.extras.complete.CompleteAutoloadTilesListener</listener-class>
</listener>
```

### **Fase 3.3.4: DWR**

#### **Tempo**: 3-5 dias

#### **Opção A: Atualizar DWR 3.0**
```bash
# 1. Remover DWR 2.x
del "Source\WEB-INF\lib\dwr-2.*.jar"

# 2. Adicionar DWR 3.0
copy "temp\new-libraries\dwr-3.0.2-RELEASE.jar" "Source\WEB-INF\lib\"
```

#### **Opção B: Migração para REST API**
```java
// Exemplo de migração DWR → REST
// ANTES (DWR)
public class UserService {
    public List<User> getUsers() {
        return userDAO.findAll();
    }
}

// DEPOIS (REST)
@RestController
@RequestMapping("/api/users")
public class UserController {
    
    @GetMapping
    public ResponseEntity<List<User>> getUsers() {
        return ResponseEntity.ok(userDAO.findAll());
    }
}
```

### **Fase 3.3.5: DisplayTag**

#### **Tempo**: 2-3 dias

#### **Migração para DataTables.js:**
```jsp
<%-- ANTES (DisplayTag) --%>
<display:table name="userList" id="user" class="table">
    <display:column property="name" title="Nome"/>
    <display:column property="email" title="Email"/>
</display:table>

<%-- DEPOIS (DataTables.js) --%>
<table id="userTable" class="table table-striped">
    <thead>
        <tr>
            <th>Nome</th>
            <th>Email</th>
        </tr>
    </thead>
    <tbody>
        <c:forEach items="${userList}" var="user">
            <tr>
                <td>${user.name}</td>
                <td>${user.email}</td>
            </tr>
        </c:forEach>
    </tbody>
</table>

<script>
$(document).ready(function() {
    $('#userTable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Portuguese-Brasil.json"
        }
    });
});
</script>
```

## Script de Download de Bibliotecas

### **download_web_frameworks.ps1**
```powershell
# Script para download de frameworks web atualizados
$libraries = @{
    "jakarta.servlet.jsp.jstl-3.0.1" = "https://repo1.maven.org/maven2/org/glassfish/web/jakarta.servlet.jsp.jstl/3.0.1/jakarta.servlet.jsp.jstl-3.0.1.jar"
    "jakarta.servlet.jsp.jstl-api-3.0.0" = "https://repo1.maven.org/maven2/jakarta/servlet/jsp/jstl/jakarta.servlet.jsp.jstl-api/3.0.0/jakarta.servlet.jsp.jstl-api-3.0.0.jar"
    "tiles-core-3.0.8" = "https://repo1.maven.org/maven2/org/apache/tiles/tiles-core/3.0.8/tiles-core-3.0.8.jar"
    "tiles-web-3.0.8" = "https://repo1.maven.org/maven2/org/apache/tiles/tiles-web/3.0.8/tiles-web-3.0.8.jar"
    "tiles-servlet-3.0.8" = "https://repo1.maven.org/maven2/org/apache/tiles/tiles-servlet/3.0.8/tiles-servlet-3.0.8.jar"
    "tiles-jsp-3.0.8" = "https://repo1.maven.org/maven2/org/apache/tiles/tiles-jsp/3.0.8/tiles-jsp-3.0.8.jar"
    "tiles-el-3.0.8" = "https://repo1.maven.org/maven2/org/apache/tiles/tiles-el/3.0.8/tiles-el-3.0.8.jar"
    "dwr-3.0.2-RELEASE" = "https://repo1.maven.org/maven2/org/directwebremoting/dwr/3.0.2-RELEASE/dwr-3.0.2-RELEASE.jar"
}

$downloadPath = "temp\web-frameworks"
New-Item -ItemType Directory -Force -Path $downloadPath

foreach ($lib in $libraries.GetEnumerator()) {
    $fileName = "$($lib.Key).jar"
    $filePath = Join-Path $downloadPath $fileName
    
    Write-Host "Downloading $($lib.Key)..."
    try {
        Invoke-WebRequest -Uri $lib.Value -OutFile $filePath
        Write-Host "✅ Downloaded: $fileName" -ForegroundColor Green
    } catch {
        Write-Host "❌ Failed to download: $($lib.Key)" -ForegroundColor Red
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "Download completed. Files saved to: $downloadPath"
```

## Testes de Validação

### **test_web_frameworks.jsp**
```jsp
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://tiles" prefix="tiles" %>

<!DOCTYPE html>
<html>
<head>
    <title>Teste de Frameworks Web - Java 21</title>
</head>
<body>
    <h1>Teste de Compatibilidade - Frameworks Web</h1>
    
    <!-- Teste JSTL -->
    <h2>Teste JSTL 3.0</h2>
    <c:set var="testMessage" value="JSTL 3.0 funcionando com Java 21!"/>
    <p><c:out value="${testMessage}"/></p>
    
    <!-- Teste Formatação -->
    <c:set var="now" value="<%=new java.util.Date()%>"/>
    <p>Data atual: <fmt:formatDate value="${now}" pattern="dd/MM/yyyy HH:mm:ss"/></p>
    
    <!-- Teste Tiles -->
    <h2>Teste Tiles 3.0</h2>
    <tiles:insertAttribute name="header" ignore="true"/>
    
    <!-- Teste DWR -->
    <h2>Teste DWR 3.0</h2>
    <script type="text/javascript" src="dwr/engine.js"></script>
    <script type="text/javascript" src="dwr/util.js"></script>
    <script>
        // Teste básico DWR
        if (typeof dwr !== 'undefined') {
            document.write('<p>✅ DWR 3.0 carregado com sucesso</p>');
        } else {
            document.write('<p>❌ DWR não carregado</p>');
        }
    </script>
    
    <h2>Status dos Frameworks</h2>
    <ul>
        <li>Servlet API 5.0: ✅ Jakarta EE</li>
        <li>JSP API 3.0: ✅ Jakarta EE</li>
        <li>JSTL 3.0: ✅ Funcionando</li>
        <li>Tiles 3.0: ✅ Funcionando</li>
        <li>DWR 3.0: <span id="dwrStatus">⏳ Verificando...</span></li>
    </ul>
</body>
</html>
```

## Checklist de Validação

### **Servlet/JSP API:**
- [ ] Imports javax → jakarta migrados
- [ ] web.xml atualizado para Servlet 5.0
- [ ] Compilação sem erros
- [ ] Deploy bem-sucedido

### **JSTL:**
- [ ] JSTL 3.0 instalado
- [ ] Tags core funcionando
- [ ] Tags fmt funcionando
- [ ] Tags fn funcionando

### **Tiles:**
- [ ] Tiles 3.0.8 instalado
- [ ] tiles.xml compatível
- [ ] Layouts funcionando
- [ ] Templates renderizando

### **DWR:**
- [ ] DWR 3.0 instalado ou alternativa implementada
- [ ] Configuração atualizada
- [ ] JavaScript funcionando
- [ ] AJAX calls funcionando

### **DisplayTag:**
- [ ] Alternativa implementada (DataTables.js)
- [ ] Tabelas funcionando
- [ ] Paginação funcionando
- [ ] Ordenação funcionando

## Próximo Passo
**[3.4 Bibliotecas de Terceiros](./3.4-bibliotecas-terceiros.md)**

---
**Status**: ⏳ Pendente
**Responsável**: Desenvolvedor Senior/Arquiteto
**Estimativa**: 1-2 semanas
