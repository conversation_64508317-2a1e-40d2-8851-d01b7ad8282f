# 3.5 Validação de Dependências

## Objetivo
Validar todas as dependências atualizadas, resolver conflitos e garantir compatibilidade entre bibliotecas.

## Pré-requisitos
- Drivers de banco atualizados (3.1)
- Bibliotecas core atualizadas (3.2)
- Frameworks web atualizados (3.3)
- Bibliotecas de terceiros atualizadas (3.4)

## Tempo Estimado
3-5 dias

## Escopo da Validação

### **Dependências a Validar:**

#### **1. Drivers de Banco**
- Oracle ojdbc11.jar
- MySQL mysql-connector-j-8.1.0.jar
- SQL Server mssql-jdbc-12.4.1.jre11.jar
- PostgreSQL postgresql-42.6.0.jar

#### **2. Bibliotecas Core**
- Apache Commons (lang3, io, collections4, etc.)
- Jackson 2.15.x
- Log4j 2.20.x
- Gson 2.10.x

#### **3. Frameworks Web**
- Jakarta Servlet API 5.0
- Jakarta JSP API 3.0
- JSTL 3.0
- Tiles 3.0.8
- DWR 3.0

#### **4. Bibliotecas Terceiros**
- iText 8.x (ou alternativa)
- Guava 32.x
- DOM4J 2.x
- Commons Imaging

#### **5. Framework Proprietário**
- PSS Framework (Visionnaire)
- VComponents

## Análise de Conflitos

### **1. Conflitos de Versão Identificados**

#### **Jackson vs Gson**
```bash
# Possível conflito entre processadores JSON
jackson-core-2.15.2.jar
jackson-databind-2.15.2.jar
gson-2.10.1.jar

# Solução: Manter ambos (usos diferentes)
# Jackson: APIs REST, configuração
# Gson: Serialização simples
```

#### **Commons Collections**
```bash
# Possível duplicação
commons-collections-3.2.jar    # Antiga
commons-collections4-4.4.jar   # Nova

# Solução: Remover versão antiga
del "Source\WEB-INF\lib\commons-collections-3.2.jar"
```

#### **Servlet API**
```bash
# Conflito javax vs jakarta
servlet-api.jar                # javax (Tomcat 8)
jakarta.servlet-api-5.0.0.jar  # jakarta (Tomcat 10)

# Solução: Remover javax, manter apenas jakarta
```

### **2. Dependências Transitivas**

#### **Log4j Dependencies**
```bash
# Log4j 2 requer múltiplos JARs
log4j-api-2.20.0.jar          # API
log4j-core-2.20.0.jar         # Implementação
log4j-slf4j-impl-2.20.0.jar   # Bridge SLF4J (se necessário)
```

#### **Tiles Dependencies**
```bash
# Tiles 3.0.8 requer múltiplos JARs
tiles-core-3.0.8.jar
tiles-web-3.0.8.jar
tiles-servlet-3.0.8.jar
tiles-jsp-3.0.8.jar
tiles-el-3.0.8.jar
```

## Scripts de Validação

### **1. validate_all_dependencies.ps1**
```powershell
# Script principal de validação de dependências
Write-Host "=== VALIDAÇÃO COMPLETA DE DEPENDÊNCIAS ===" -ForegroundColor Yellow
Write-Host ""

$libPath = "Source\WEB-INF\lib"
$issues = @()
$warnings = @()
$success = @()

# Função para verificar biblioteca
function Test-Library {
    param($name, $pattern, $required = $true)
    
    $found = Get-ChildItem "$libPath\$pattern" -ErrorAction SilentlyContinue
    
    if ($found) {
        if ($found.Count -gt 1) {
            $script:warnings += "⚠️ Múltiplas versões de $name encontradas: $($found.Name -join ', ')"
        } else {
            $script:success += "✅ $name: $($found.Name)"
        }
        return $true
    } else {
        if ($required) {
            $script:issues += "❌ $name: Não encontrado (padrão: $pattern)"
        } else {
            $script:warnings += "⚠️ $name: Não encontrado (opcional)"
        }
        return $false
    }
}

Write-Host "=== DRIVERS DE BANCO ===" -ForegroundColor Cyan
Test-Library "Oracle JDBC" "ojdbc11*.jar"
Test-Library "MySQL Connector" "mysql-connector-j-8*.jar"
Test-Library "SQL Server JDBC" "mssql-jdbc-12*.jar"
Test-Library "PostgreSQL JDBC" "postgresql-42.6*.jar"

Write-Host ""
Write-Host "=== BIBLIOTECAS CORE ===" -ForegroundColor Cyan
Test-Library "Commons Lang3" "commons-lang3-3*.jar"
Test-Library "Commons IO" "commons-io-2*.jar"
Test-Library "Jackson Core" "jackson-core-2.15*.jar"
Test-Library "Jackson Databind" "jackson-databind-2.15*.jar"
Test-Library "Log4j API" "log4j-api-2*.jar"
Test-Library "Log4j Core" "log4j-core-2*.jar"

Write-Host ""
Write-Host "=== FRAMEWORKS WEB ===" -ForegroundColor Cyan
Test-Library "JSTL" "jakarta.servlet.jsp.jstl-3*.jar"
Test-Library "Tiles Core" "tiles-core-3*.jar"
Test-Library "DWR" "dwr-3*.jar" $false

Write-Host ""
Write-Host "=== BIBLIOTECAS TERCEIROS ===" -ForegroundColor Cyan
Test-Library "Gson" "gson-2.10*.jar"
Test-Library "Guava" "guava-32*.jar"
Test-Library "DOM4J" "dom4j-2*.jar" $false
Test-Library "PSS Framework" "pss.jar"

Write-Host ""
Write-Host "=== VERIFICAÇÃO DE CONFLITOS ===" -ForegroundColor Cyan

# Verificar duplicatas
$allJars = Get-ChildItem "$libPath\*.jar" | ForEach-Object { $_.Name }
$duplicates = $allJars | Group-Object { ($_ -split '-')[0] } | Where-Object { $_.Count -gt 1 }

if ($duplicates) {
    Write-Host "⚠️ Possíveis duplicatas encontradas:" -ForegroundColor Yellow
    foreach ($dup in $duplicates) {
        $warnings += "⚠️ Duplicata: $($dup.Name) - $($dup.Group -join ', ')"
    }
}

# Verificar bibliotecas antigas
$oldLibs = @(
    "servlet-api.jar",
    "jsp-api.jar", 
    "jstl-1*.jar",
    "commons-lang-2*.jar",
    "commons-collections-3*.jar",
    "jackson-*-2.6*.jar",
    "log4j-1*.jar",
    "mysql-connector-java-5*.jar",
    "ojdbc[678].jar",
    "elasticsearch-2*.jar"
)

foreach ($pattern in $oldLibs) {
    $found = Get-ChildItem "$libPath\$pattern" -ErrorAction SilentlyContinue
    if ($found) {
        $issues += "❌ Biblioteca antiga encontrada: $($found.Name)"
    }
}

# Relatório final
Write-Host ""
Write-Host "=== RELATÓRIO FINAL ===" -ForegroundColor Yellow

if ($success.Count -gt 0) {
    Write-Host ""
    Write-Host "SUCESSOS ($($success.Count)):" -ForegroundColor Green
    $success | ForEach-Object { Write-Host $_ -ForegroundColor Green }
}

if ($warnings.Count -gt 0) {
    Write-Host ""
    Write-Host "AVISOS ($($warnings.Count)):" -ForegroundColor Yellow
    $warnings | ForEach-Object { Write-Host $_ -ForegroundColor Yellow }
}

if ($issues.Count -gt 0) {
    Write-Host ""
    Write-Host "PROBLEMAS ($($issues.Count)):" -ForegroundColor Red
    $issues | ForEach-Object { Write-Host $_ -ForegroundColor Red }
    Write-Host ""
    Write-Host "❌ VALIDAÇÃO FALHOU - Corrija os problemas antes de prosseguir" -ForegroundColor Red
} else {
    Write-Host ""
    Write-Host "✅ VALIDAÇÃO PASSOU - Todas as dependências estão corretas" -ForegroundColor Green
}

Write-Host ""
Write-Host "Total de JARs: $($allJars.Count)" -ForegroundColor Cyan
Write-Host "Tamanho total: $([math]::Round((Get-ChildItem $libPath\*.jar | Measure-Object Length -Sum).Sum / 1MB, 2)) MB" -ForegroundColor Cyan
```

### **2. check_classpath_conflicts.java**
```java
import java.io.File;
import java.net.URL;
import java.net.URLClassLoader;
import java.util.*;

public class CheckClasspathConflicts {
    
    public static void main(String[] args) {
        System.out.println("=== VERIFICAÇÃO DE CONFLITOS DE CLASSPATH ===");
        System.out.println();
        
        // Verificar classes duplicadas
        checkDuplicateClasses();
        
        // Verificar versões de bibliotecas
        checkLibraryVersions();
        
        // Verificar compatibilidade de APIs
        checkAPICompatibility();
    }
    
    private static void checkDuplicateClasses() {
        System.out.println("=== VERIFICAÇÃO DE CLASSES DUPLICADAS ===");
        
        String[] criticalClasses = {
            "org.apache.commons.lang3.StringUtils",
            "com.fasterxml.jackson.databind.ObjectMapper",
            "org.apache.logging.log4j.LogManager",
            "jakarta.servlet.http.HttpServlet",
            "com.google.gson.Gson"
        };
        
        for (String className : criticalClasses) {
            try {
                Class<?> clazz = Class.forName(className);
                URL location = clazz.getProtectionDomain().getCodeSource().getLocation();
                System.out.println("✅ " + className + " → " + location);
            } catch (ClassNotFoundException e) {
                System.out.println("❌ " + className + " → Não encontrada");
            }
        }
        System.out.println();
    }
    
    private static void checkLibraryVersions() {
        System.out.println("=== VERIFICAÇÃO DE VERSÕES ===");
        
        // Jackson version
        try {
            String jacksonVersion = com.fasterxml.jackson.databind.cfg.PackageVersion.VERSION.toString();
            System.out.println("✅ Jackson: " + jacksonVersion);
        } catch (Exception e) {
            System.out.println("❌ Jackson: Erro ao obter versão");
        }
        
        // Gson version
        try {
            // Gson não expõe versão facilmente, verificar apenas presença
            new com.google.gson.Gson();
            System.out.println("✅ Gson: Presente");
        } catch (Exception e) {
            System.out.println("❌ Gson: Não encontrado");
        }
        
        // Log4j version
        try {
            String log4jVersion = org.apache.logging.log4j.LogManager.class.getPackage().getImplementationVersion();
            System.out.println("✅ Log4j: " + (log4jVersion != null ? log4jVersion : "Versão não detectada"));
        } catch (Exception e) {
            System.out.println("❌ Log4j: Erro ao verificar");
        }
        
        System.out.println();
    }
    
    private static void checkAPICompatibility() {
        System.out.println("=== VERIFICAÇÃO DE COMPATIBILIDADE DE APIs ===");
        
        // Verificar Jakarta vs javax
        try {
            Class.forName("jakarta.servlet.http.HttpServlet");
            System.out.println("✅ Jakarta Servlet API: Presente");
        } catch (ClassNotFoundException e) {
            System.out.println("❌ Jakarta Servlet API: Não encontrada");
        }
        
        try {
            Class.forName("javax.servlet.http.HttpServlet");
            System.out.println("⚠️ javax Servlet API: Ainda presente (deve ser removida)");
        } catch (ClassNotFoundException e) {
            System.out.println("✅ javax Servlet API: Removida corretamente");
        }
        
        // Verificar JSTL
        try {
            Class.forName("jakarta.servlet.jsp.jstl.core.Config");
            System.out.println("✅ JSTL 3.0: Presente");
        } catch (ClassNotFoundException e) {
            System.out.println("❌ JSTL 3.0: Não encontrada");
        }
        
        // Verificar PSS Framework
        try {
            Class.forName("com.visionnaire.PSS.client.StorageObjectBase");
            System.out.println("✅ PSS Framework: Presente");
        } catch (ClassNotFoundException e) {
            System.out.println("❌ PSS Framework: Não encontrado");
        }
        
        System.out.println();
    }
}
```

### **3. test_database_connections.java**
```java
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

public class TestDatabaseConnections {
    
    public static void main(String[] args) {
        System.out.println("=== TESTE DE CONECTIVIDADE COM BANCOS ===");
        System.out.println();
        
        testAllDatabases();
    }
    
    private static void testAllDatabases() {
        // Oracle
        testDatabase("Oracle", 
            "oracle.jdbc.OracleDriver",
            "**********************************************",
            "WEBP_CRM_DEV", "mSKqzKHzdDJk");
        
        // MySQL
        testDatabase("MySQL",
            "com.mysql.cj.jdbc.Driver",
            "********************************************************************************",
            "root", "a4DqYcBvKB");
        
        // SQL Server
        testDatabase("SQL Server",
            "com.microsoft.sqlserver.jdbc.SQLServerDriver",
            "*************************************************************************",
            "sa", "a4DqYcBvKB");
        
        // PostgreSQL
        testDatabase("PostgreSQL",
            "org.postgresql.Driver",
            "***********************************************",
            "postgres", "a4DqYcBvKB");
    }
    
    private static void testDatabase(String name, String driver, String url, String user, String pass) {
        System.out.println("Testando " + name + "...");
        try {
            Class.forName(driver);
            Connection conn = DriverManager.getConnection(url, user, pass);
            System.out.println("✅ " + name + ": Conexão bem-sucedida");
            
            // Teste básico de query
            var stmt = conn.createStatement();
            var rs = stmt.executeQuery("SELECT 1" + (name.equals("Oracle") ? " FROM DUAL" : ""));
            if (rs.next()) {
                System.out.println("✅ " + name + ": Query de teste executada");
            }
            
            conn.close();
        } catch (ClassNotFoundException e) {
            System.out.println("❌ " + name + ": Driver não encontrado - " + e.getMessage());
        } catch (SQLException e) {
            System.out.println("❌ " + name + ": Erro de conexão - " + e.getMessage());
        } catch (Exception e) {
            System.out.println("❌ " + name + ": Erro inesperado - " + e.getMessage());
        }
        System.out.println();
    }
}
```

## Resolução de Conflitos

### **1. Script de Limpeza**
```batch
@echo off
echo ========================================
echo    LIMPEZA DE DEPENDÊNCIAS CONFLITANTES
echo ========================================
echo.

echo === REMOVENDO BIBLIOTECAS ANTIGAS ===

REM Servlet API antiga
del "Source\WEB-INF\lib\servlet-api.jar" 2>nul
del "Source\WEB-INF\lib\jsp-api.jar" 2>nul

REM JSTL antigo
del "Source\WEB-INF\lib\jstl-1*.jar" 2>nul
del "Source\WEB-INF\lib\standard-1*.jar" 2>nul

REM Commons antigos
del "Source\WEB-INF\lib\commons-lang-2*.jar" 2>nul
del "Source\WEB-INF\lib\commons-collections-3*.jar" 2>nul

REM Jackson antigo
del "Source\WEB-INF\lib\jackson-*-2.6*.jar" 2>nul
del "Source\WEB-INF\lib\jackson-*-2.8*.jar" 2>nul

REM Log4j antigo
del "Source\WEB-INF\lib\log4j-1*.jar" 2>nul
del "Source\WEB-INF\lib\apache-log4j-extras*.jar" 2>nul

REM Drivers antigos
del "Source\WEB-INF\lib\mysql-connector-java-5*.jar" 2>nul
del "Source\WEB-INF\lib\ojdbc6.jar" 2>nul
del "Source\WEB-INF\lib\ojdbc7.jar" 2>nul
del "Source\WEB-INF\lib\ojdbc8.jar" 2>nul
del "Source\WEB-INF\lib\sqljdbc4.jar" 2>nul
del "Source\WEB-INF\lib\sqljdbc6.jar" 2>nul

REM Outras bibliotecas antigas
del "Source\WEB-INF\lib\elasticsearch-2*.jar" 2>nul
del "Source\WEB-INF\lib\boon-*.jar" 2>nul
del "Source\WEB-INF\lib\jai_*.jar" 2>nul

echo ✅ Limpeza concluída

echo.
echo === VERIFICANDO RESULTADO ===
echo Bibliotecas restantes:
dir "Source\WEB-INF\lib\*.jar" | find /c ".jar"

echo.
echo ✅ Limpeza de conflitos concluída!
pause
```

### **2. Matriz de Dependências Final**

#### **dependencies_matrix.md**
```markdown
# Matriz Final de Dependências - Java 21

## Drivers de Banco
| Biblioteca | Versão | Tamanho | Status |
|------------|--------|---------|---------|
| ojdbc11.jar | 21.9.0.0 | ~4MB | ✅ |
| mysql-connector-j-8.1.0.jar | 8.1.0 | ~2.5MB | ✅ |
| mssql-jdbc-12.4.1.jre11.jar | 12.4.1 | ~1.2MB | ✅ |
| postgresql-42.6.0.jar | 42.6.0 | ~1.1MB | ✅ |

## Bibliotecas Core
| Biblioteca | Versão | Tamanho | Status |
|------------|--------|---------|---------|
| commons-lang3-3.12.0.jar | 3.12.0 | ~640KB | ✅ |
| commons-io-2.11.0.jar | 2.11.0 | ~350KB | ✅ |
| jackson-core-2.15.2.jar | 2.15.2 | ~365KB | ✅ |
| jackson-databind-2.15.2.jar | 2.15.2 | ~1.5MB | ✅ |
| log4j-api-2.20.0.jar | 2.20.0 | ~300KB | ✅ |
| log4j-core-2.20.0.jar | 2.20.0 | ~1.8MB | ✅ |

## Frameworks Web
| Biblioteca | Versão | Tamanho | Status |
|------------|--------|---------|---------|
| jakarta.servlet.jsp.jstl-3.0.1.jar | 3.0.1 | ~400KB | ✅ |
| tiles-core-3.0.8.jar | 3.0.8 | ~350KB | ✅ |
| tiles-jsp-3.0.8.jar | 3.0.8 | ~50KB | ✅ |
| dwr-3.0.2-RELEASE.jar | 3.0.2 | ~500KB | ✅ |

## Bibliotecas Terceiros
| Biblioteca | Versão | Tamanho | Status |
|------------|--------|---------|---------|
| gson-2.10.1.jar | 2.10.1 | ~270KB | ✅ |
| guava-32.1.3-jre.jar | 32.1.3 | ~2.8MB | ✅ |
| dom4j-2.1.4.jar | 2.1.4 | ~320KB | ✅ |

## Framework Proprietário
| Biblioteca | Versão | Tamanho | Status |
|------------|--------|---------|---------|
| pss.jar | 3.0 | ~2MB | ⚠️ Verificar |
| acss-local.jar | 2.0 | ~1MB | ⚠️ Verificar |

**Total**: ~95 JARs, ~25MB
```

## Checklist Final de Validação

### **Dependências Obrigatórias:**
- [ ] Todos os drivers de banco presentes e funcionando
- [ ] Bibliotecas core atualizadas e compatíveis
- [ ] Frameworks web migrados para Jakarta EE
- [ ] Bibliotecas terceiros atualizadas
- [ ] PSS Framework compatível

### **Conflitos Resolvidos:**
- [ ] Nenhuma biblioteca javax.servlet presente
- [ ] Nenhuma versão duplicada de bibliotecas
- [ ] Nenhuma biblioteca antiga incompatível
- [ ] Dependências transitivas corretas

### **Testes Funcionais:**
- [ ] Compilação sem erros
- [ ] Conectividade com todos os bancos
- [ ] Carregamento de todas as classes críticas
- [ ] Serialização JSON funcionando
- [ ] Logging funcionando

### **Performance:**
- [ ] Tempo de startup aceitável
- [ ] Uso de memória dentro do esperado
- [ ] Nenhum memory leak detectado
- [ ] Garbage collection estável

## Próximo Passo
**[4.1 Atualização do web.xml](./4.1-atualizacao-webxml.md)**

---
**Status**: ⏳ Pendente
**Responsável**: Arquiteto de Software
**Estimativa**: 3-5 dias
