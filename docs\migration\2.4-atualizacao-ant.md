# 2.4 Atualização do Ant Build

## Objetivo
Atualizar o sistema de build Apache Ant para trabalhar com Java 21, Tomcat 10.1.15 e as novas dependências.

## Pré-requisitos
- JDK 21 instalado e configurado
- Tomcat 10.1.15 instalado
- Eclipse/IDE configurado
- Apache Ant 1.10.x instalado

## Tempo Estimado
3-4 horas

## Análise da Situação Atual

### **Build Atual Identificado:**
- **Ant Version**: Provavelmente 1.9.x ou anterior
- **Java Target**: 1.8
- **Tomcat**: 8.0.53
- **Servlet API**: 3.1
- **Encoding**: Pode estar em ISO-8859-1

### **Arquivos de Build Atuais:**
- `Source/build.xml` - Script principal do Ant
- `Source/ant.properties` - Propriedades de configuração
- Possíveis scripts auxiliares

## Passos Detalhados

### 1. Verificação e Atualização do Apache Ant

#### 1.1 Verificar Versão Atual
```bash
ant -version
# Versão mínima requerida: Apache Ant 1.10.12
# Versão recomendada: Apache Ant 1.10.14
```

#### 1.2 Atualizar Ant (se necessário)
```bash
# Download do Apache Ant 1.10.14
# URL: https://ant.apache.org/bindownload.cgi

# Extrair para: D:\java\apache-ant-1.10.14
# Configurar ANT_HOME
setx ANT_HOME "D:\java\apache-ant-1.10.14" /M
setx PATH "%ANT_HOME%\bin;%PATH%" /M
```

### 2. Backup dos Arquivos de Build

```bash
# Criar backup dos arquivos de build atuais
mkdir "backup\build-configs"
copy "Source\build.xml" "backup\build-configs\build-original.xml"
copy "Source\ant.properties" "backup\build-configs\ant-original.properties"

# Documentar configuração atual
echo === BUILD CONFIGURATION BACKUP === > "backup\build-configs\README.txt"
echo Date: %date% >> "backup\build-configs\README.txt"
echo Original Java Version: 8 >> "backup\build-configs\README.txt"
echo Original Tomcat: 8.0.53 >> "backup\build-configs\README.txt"
```

### 3. Atualização do ant.properties

#### 3.1 Configurações Java 21
```properties
# ant.properties - Configurações para Java 21

# === JAVA CONFIGURATION ===
java.home=C:/Program Files/Eclipse Adoptium/jdk-*********-hotspot
java.version=21
java.target.version=21
java.source.version=21

# Manter configuração antiga comentada para referência
#java.home.old=D:/java/jdk1.8.0_362
#java.version.old=1.8

# === TOMCAT CONFIGURATION ===
tomcat.home=D:/java/apache-tomcat-10.1.15
tomcat.version=10.1.15
servlet.api.version=5.0

# Configuração antiga comentada
#tomcat.home.old=D:/java/apache-tomcat-8.0.53
#servlet.api.version.old=3.1

# === PSS FRAMEWORK ===
pss.home=V:/Visionnaire/PSS/Dist-3.0
acss.lib=V:/Visionnaire/PesquisaDesenvolvimento/ACSS/Dist-2.0/lib

# === BUILD DIRECTORIES ===
src.dir=WEB-INF/src
classes.dir=WEB-INF/classes
lib.dir=WEB-INF/lib
web.dir=.
dist.dir=dist
temp.dir=temp

# === COMPILATION SETTINGS ===
compile.debug=true
compile.deprecation=true
compile.optimize=false
compile.encoding=UTF-8

# === DEPLOYMENT SETTINGS ===
app.name=webp
app.version=1.0
war.name=${app.name}.war

# === DATABASE CONFIGURATIONS ===
# Oracle CRM
db.oracle.driver=oracle.jdbc.OracleDriver
db.oracle.url=**********************************************
db.oracle.username=WEBP_CRM_DEV
db.oracle.password=mSKqzKHzdDJk

# MySQL Visionnaire
db.mysql.driver=com.mysql.cj.jdbc.Driver
db.mysql.url=jdbc:mysql://**************:3306/dev_visionnaire?useSSL=false&serverTimezone=UTC
db.mysql.username=root
db.mysql.password=a4DqYcBvKB

# SQL Server FIEPE
db.sqlserver.driver=com.microsoft.sqlserver.jdbc.SQLServerDriver
db.sqlserver.url=jdbc:sqlserver://**************:1433;databaseName=dev_fiepe
db.sqlserver.username=sa
db.sqlserver.password=a4DqYcBvKB

# PostgreSQL CITS
db.postgresql.driver=org.postgresql.Driver
db.postgresql.url=***********************************************
db.postgresql.username=postgres
db.postgresql.password=a4DqYcBvKB
```

### 4. Atualização do build.xml

#### 4.1 Cabeçalho e Propriedades
```xml
<?xml version="1.0" encoding="UTF-8"?>
<project name="WebPublication" default="build" basedir=".">
    
    <description>
        WebPublication Build Script - Java 21 / Tomcat 10.1 / Jakarta EE
    </description>
    
    <!-- Load properties -->
    <property file="ant.properties"/>
    
    <!-- Build timestamp -->
    <tstamp>
        <format property="build.timestamp" pattern="yyyy-MM-dd HH:mm:ss"/>
        <format property="build.date" pattern="yyyyMMdd"/>
    </tstamp>
    
    <!-- Version info -->
    <property name="build.version" value="${app.version}.${build.date}"/>
    
    <!-- Paths -->
    <path id="compile.classpath">
        <fileset dir="${lib.dir}">
            <include name="**/*.jar"/>
        </fileset>
        <fileset dir="${tomcat.home}/lib">
            <include name="servlet-api.jar"/>
            <include name="jsp-api.jar"/>
            <include name="el-api.jar"/>
        </fileset>
    </path>
    
    <path id="runtime.classpath">
        <path refid="compile.classpath"/>
        <pathelement location="${classes.dir}"/>
    </path>
```

#### 4.2 Targets Básicos
```xml
    <!-- Clean target -->
    <target name="clean" description="Clean build directories">
        <echo message="Cleaning build directories..."/>
        <delete dir="${classes.dir}" quiet="true"/>
        <delete dir="${dist.dir}" quiet="true"/>
        <delete dir="${temp.dir}" quiet="true"/>
        <echo message="Clean completed."/>
    </target>
    
    <!-- Init target -->
    <target name="init" description="Initialize build">
        <echo message="Initializing build for WebPublication ${build.version}"/>
        <echo message="Java Home: ${java.home}"/>
        <echo message="Java Version: ${java.version}"/>
        <echo message="Tomcat Home: ${tomcat.home}"/>
        <echo message="Build Time: ${build.timestamp}"/>
        
        <mkdir dir="${classes.dir}"/>
        <mkdir dir="${dist.dir}"/>
        <mkdir dir="${temp.dir}"/>
    </target>
    
    <!-- Compile target -->
    <target name="compile" depends="init" description="Compile Java sources">
        <echo message="Compiling Java sources with Java ${java.version}..."/>
        
        <javac srcdir="${src.dir}"
               destdir="${classes.dir}"
               classpathref="compile.classpath"
               debug="${compile.debug}"
               deprecation="${compile.deprecation}"
               optimize="${compile.optimize}"
               encoding="${compile.encoding}"
               source="${java.source.version}"
               target="${java.target.version}"
               includeantruntime="false">
            
            <!-- Compiler arguments for Java 21 -->
            <compilerarg value="-Xlint:unchecked"/>
            <compilerarg value="-Xlint:deprecation"/>
            <compilerarg value="--enable-preview" if="java.enable.preview"/>
        </javac>
        
        <!-- Copy non-Java resources -->
        <copy todir="${classes.dir}">
            <fileset dir="${src.dir}">
                <exclude name="**/*.java"/>
                <include name="**/*.properties"/>
                <include name="**/*.xml"/>
                <include name="**/*.txt"/>
            </fileset>
        </copy>
        
        <echo message="Compilation completed successfully."/>
    </target>
```

#### 4.3 Targets de Build e Deploy
```xml
    <!-- Build WAR target -->
    <target name="war" depends="compile" description="Build WAR file">
        <echo message="Building WAR file: ${war.name}"/>
        
        <war destfile="${dist.dir}/${war.name}" webxml="WEB-INF/web.xml">
            <!-- Web content -->
            <fileset dir="${web.dir}">
                <exclude name="WEB-INF/src/**"/>
                <exclude name="WEB-INF/classes/**"/>
                <exclude name="build.xml"/>
                <exclude name="ant.properties"/>
                <exclude name="temp/**"/>
                <exclude name="dist/**"/>
                <exclude name="backup/**"/>
                <exclude name="docs/**"/>
                <exclude name="*.bat"/>
                <exclude name="*.ps1"/>
                <exclude name="*.md"/>
            </fileset>
            
            <!-- Compiled classes -->
            <classes dir="${classes.dir}"/>
            
            <!-- Libraries -->
            <lib dir="${lib.dir}">
                <exclude name="servlet-api*.jar"/>
                <exclude name="jsp-api*.jar"/>
                <exclude name="el-api*.jar"/>
            </lib>
            
            <!-- Manifest -->
            <manifest>
                <attribute name="Built-By" value="${user.name}"/>
                <attribute name="Built-Date" value="${build.timestamp}"/>
                <attribute name="Implementation-Title" value="WebPublication"/>
                <attribute name="Implementation-Version" value="${build.version}"/>
                <attribute name="Implementation-Vendor" value="Visionnaire"/>
                <attribute name="Java-Version" value="${java.version}"/>
                <attribute name="Servlet-Version" value="${servlet.api.version}"/>
            </manifest>
        </war>
        
        <echo message="WAR file created: ${dist.dir}/${war.name}"/>
    </target>
    
    <!-- Deploy to Tomcat -->
    <target name="deploy" depends="war" description="Deploy WAR to Tomcat">
        <echo message="Deploying to Tomcat ${tomcat.version}..."/>
        
        <!-- Stop Tomcat if running -->
        <exec executable="${tomcat.home}/bin/shutdown.bat" 
              spawn="false" 
              failonerror="false"/>
        
        <!-- Wait for shutdown -->
        <sleep seconds="5"/>
        
        <!-- Remove old deployment -->
        <delete dir="${tomcat.home}/webapps/${app.name}" quiet="true"/>
        <delete file="${tomcat.home}/webapps/${war.name}" quiet="true"/>
        
        <!-- Copy new WAR -->
        <copy file="${dist.dir}/${war.name}" 
              todir="${tomcat.home}/webapps"/>
        
        <!-- Start Tomcat -->
        <exec executable="${tomcat.home}/bin/startup.bat" 
              spawn="true"/>
        
        <echo message="Deployment completed. Application will be available at:"/>
        <echo message="http://localhost:8080/${app.name}"/>
    </target>
```

#### 4.4 Targets de Teste e Validação
```xml
    <!-- Test compilation -->
    <target name="test-compile" depends="compile" description="Test compilation only">
        <echo message="Testing compilation with Java ${java.version}..."/>
        <echo message="Compilation test completed successfully."/>
    </target>
    
    <!-- Validate dependencies -->
    <target name="validate-deps" description="Validate dependencies">
        <echo message="Validating dependencies..."/>
        
        <!-- Check required JARs -->
        <available file="${lib.dir}/pss.jar" property="pss.available"/>
        <fail unless="pss.available" message="PSS framework not found: ${lib.dir}/pss.jar"/>
        
        <available file="${lib.dir}/mysql-connector-java-8.0.33.jar" property="mysql.available"/>
        <echo message="MySQL driver: ${mysql.available}" if="mysql.available"/>
        
        <available file="${lib.dir}/ojdbc11.jar" property="oracle.available"/>
        <echo message="Oracle driver: ${oracle.available}" if="oracle.available"/>
        
        <!-- Check Tomcat -->
        <available file="${tomcat.home}/lib/servlet-api.jar" property="tomcat.available"/>
        <fail unless="tomcat.available" message="Tomcat not found: ${tomcat.home}"/>
        
        <echo message="Dependencies validation completed."/>
    </target>
    
    <!-- Environment info -->
    <target name="info" description="Display build environment information">
        <echo message="=== WebPublication Build Environment ==="/>
        <echo message="Project: ${ant.project.name}"/>
        <echo message="Version: ${build.version}"/>
        <echo message="Build Date: ${build.timestamp}"/>
        <echo message=""/>
        <echo message="Java Home: ${java.home}"/>
        <echo message="Java Version: ${java.version}"/>
        <echo message="Ant Version: ${ant.version}"/>
        <echo message=""/>
        <echo message="Tomcat Home: ${tomcat.home}"/>
        <echo message="Tomcat Version: ${tomcat.version}"/>
        <echo message="Servlet API: ${servlet.api.version}"/>
        <echo message=""/>
        <echo message="Source Dir: ${src.dir}"/>
        <echo message="Classes Dir: ${classes.dir}"/>
        <echo message="Lib Dir: ${lib.dir}"/>
        <echo message="Dist Dir: ${dist.dir}"/>
        <echo message=""/>
        <echo message="Compile Encoding: ${compile.encoding}"/>
        <echo message="Compile Debug: ${compile.debug}"/>
        <echo message="====================================="/>
    </target>
```

#### 4.5 Target Principal
```xml
    <!-- Main build target -->
    <target name="build" depends="clean,compile,war" description="Complete build">
        <echo message="=== Build Summary ==="/>
        <echo message="Project: ${ant.project.name}"/>
        <echo message="Version: ${build.version}"/>
        <echo message="WAR File: ${dist.dir}/${war.name}"/>
        <echo message="Build completed successfully at ${build.timestamp}"/>
        <echo message="===================="/>
    </target>
    
    <!-- All target -->
    <target name="all" depends="clean,validate-deps,compile,war,deploy" description="Complete build and deploy">
        <echo message="Complete build and deployment finished."/>
    </target>

</project>
```

### 5. Scripts de Build Auxiliares

#### 5.1 build.bat
```batch
@echo off
echo ========================================
echo    WEBPUBLICATION BUILD SCRIPT
echo ========================================
echo.

if "%1"=="" (
    echo Usage: build.bat [target]
    echo.
    echo Available targets:
    echo   clean      - Clean build directories
    echo   compile    - Compile Java sources
    echo   war        - Build WAR file
    echo   deploy     - Deploy to Tomcat
    echo   build      - Complete build ^(default^)
    echo   all        - Build and deploy
    echo   info       - Show environment info
    echo.
    set TARGET=build
) else (
    set TARGET=%1
)

echo Building target: %TARGET%
echo.

ant %TARGET%

if %errorlevel% equ 0 (
    echo.
    echo ✅ Build completed successfully!
) else (
    echo.
    echo ❌ Build failed with errors.
)

pause
```

#### 5.2 quick-deploy.bat
```batch
@echo off
echo ========================================
echo    QUICK DEPLOY TO TOMCAT
echo ========================================
echo.

echo Stopping Tomcat...
call "D:\java\apache-tomcat-10.1.15\bin\shutdown.bat"
timeout /t 5 /nobreak

echo Building WAR...
ant war

if %errorlevel% neq 0 (
    echo ❌ Build failed!
    pause
    exit /b 1
)

echo Deploying WAR...
copy "dist\webp.war" "D:\java\apache-tomcat-10.1.15\webapps\"

echo Starting Tomcat...
call "D:\java\apache-tomcat-10.1.15\bin\startup.bat"

echo.
echo ✅ Quick deploy completed!
echo Application will be available at: http://localhost:8080/webp
echo.
pause
```

### 6. Validação e Testes

#### 6.1 Teste de Configuração
```bash
# Verificar configuração
ant info

# Validar dependências
ant validate-deps

# Teste de compilação
ant test-compile
```

#### 6.2 Teste de Build Completo
```bash
# Build completo
ant build

# Verificar WAR gerado
dir dist\*.war

# Verificar conteúdo do WAR
jar -tf dist\webp.war | head -20
```

### 7. Troubleshooting

#### Problema: Ant não encontra Java 21
```
Solução:
1. Verificar JAVA_HOME no ant.properties
2. Verificar se JDK 21 está no PATH
3. Usar ANT_OPTS=-Djava.home="C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot"
```

#### Problema: Compilação falha
```
Soluções:
1. Verificar source/target version no build.xml
2. Verificar encoding UTF-8
3. Verificar classpath das bibliotecas
4. Limpar e recompilar: ant clean compile
```

#### Problema: WAR não deploya
```
Soluções:
1. Verificar se Tomcat 10.1 está configurado
2. Verificar web.xml (deve ser Servlet 5.0)
3. Verificar logs do Tomcat
4. Verificar permissões de arquivo
```

### 8. Checklist de Validação

#### Ant Configuration:
- [ ] Apache Ant 1.10.14 instalado
- [ ] ANT_HOME configurado
- [ ] ant.properties atualizado para Java 21
- [ ] build.xml atualizado para Java 21

#### Build Process:
- [ ] `ant info` mostra configurações corretas
- [ ] `ant validate-deps` passa sem erros
- [ ] `ant compile` compila sem erros
- [ ] `ant war` gera WAR corretamente
- [ ] `ant deploy` deploya no Tomcat 10.1

#### Integration:
- [ ] Build funciona no Eclipse
- [ ] Build funciona via linha de comando
- [ ] Scripts auxiliares funcionam
- [ ] Deploy automático funciona

## Próximo Passo
**[3.1 Drivers de Banco de Dados](./3.1-drivers-banco.md)**

---
**Status**: ⏳ Pendente
**Responsável**: Desenvolvedor/DevOps
**Estimativa**: 3-4 horas
