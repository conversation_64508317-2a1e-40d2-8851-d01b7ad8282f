# 4.3 Propriedades da Aplicação

## Objetivo
Atualizar e configurar arquivos de propriedades, configurações XML e parâmetros da aplicação WebPublication para compatibilidade com Java 21.

## Pré-requisitos
- Tomcat 10.1.15 configurado (4.2)
- web.xml atualizado (4.1)
- Dependências atualizadas (Fase 3)

## Tempo Estimado
2-3 dias

## Arquivos de Propriedades Identificados

### **Baseado na Análise do Código Fonte:**

#### **1. Arquivos .properties**
- **application.properties** - Configurações gerais
- **database.properties** - Configurações de banco
- **log4j.properties** - Configurações de log (migrar para log4j2.xml)
- **messages.properties** - Mensagens de internacionalização
- **config.properties** - Configurações específicas

#### **2. Arquivos XML de Configuração**
- **tiles.xml** - Configuração do Apache Tiles
- **dwr.xml** - Configuração do DWR
- **spring-config.xml** - Configurações Spring (se presente)
- **hibernate.cfg.xml** - Configurações Hibernate (se presente)

#### **3. Arquivos de Recursos**
- **messages_pt_BR.properties** - Mensagens em português
- **messages_en_US.properties** - Mensagens em inglês
- **validation.properties** - Regras de validação

## Análise e Migração por Arquivo

### **1. application.properties**

#### **Localização Provável**: `WEB-INF/classes/application.properties`

#### **Configurações Típicas a Atualizar:**

```properties
# ========================================
# WebPublication Application Properties
# Java 21 / Jakarta EE Compatible
# ========================================

# === APPLICATION INFO ===
app.name=WebPublication
app.version=2.0.0
app.environment=development
app.build.date=2024-01-15
app.java.version=21

# === SERVER CONFIGURATION ===
server.port=8080
server.context.path=/webp
server.session.timeout=30
server.encoding=UTF-8

# === DATABASE CONFIGURATION ===
# Oracle CRM
db.oracle.jndi.name=java:comp/env/jdbc/WebpCrmDev
db.oracle.pool.initial=5
db.oracle.pool.max=20
db.oracle.pool.timeout=10000

# MySQL Visionnaire
db.mysql.jndi.name=java:comp/env/jdbc/VisionnaireDevMysql
db.mysql.pool.initial=3
db.mysql.pool.max=15
db.mysql.pool.timeout=10000

# SQL Server FIEPE
db.sqlserver.jndi.name=java:comp/env/jdbc/FiepeDevSqlserver
db.sqlserver.pool.initial=3
db.sqlserver.pool.max=15
db.sqlserver.pool.timeout=10000

# PostgreSQL CITS
db.postgresql.jndi.name=java:comp/env/jdbc/CitsProdPostgresql
db.postgresql.pool.initial=3
db.postgresql.pool.max=15
db.postgresql.pool.timeout=10000

# === PSS FRAMEWORK ===
pss.home=V:/Visionnaire/PSS/Dist-3.0
pss.config.path=${pss.home}/config
pss.cache.enabled=true
pss.cache.size=1000
pss.debug.enabled=false

# === ACSS CONFIGURATION ===
acss.lib.path=V:/Visionnaire/PesquisaDesenvolvimento/ACSS/Dist-2.0/lib
acss.config.path=${acss.lib.path}/config
acss.enabled=true

# === FILE UPLOAD ===
upload.max.file.size=10485760
upload.max.request.size=52428800
upload.temp.directory=${java.io.tmpdir}/webpublication/uploads
upload.allowed.extensions=pdf,doc,docx,xls,xlsx,jpg,jpeg,png,gif

# === SECURITY ===
security.session.secure=false
security.csrf.enabled=true
security.xss.protection=true
security.content.type.nosniff=true

# === LOGGING ===
logging.level.root=INFO
logging.level.com.visionnaire.webpublication=DEBUG
logging.level.com.visionnaire.PSS=INFO
logging.level.org.apache.tiles=WARN
logging.level.org.directwebremoting=WARN

# === CACHE CONFIGURATION ===
cache.enabled=true
cache.provider=ehcache
cache.config.file=ehcache.xml
cache.default.ttl=3600

# === EMAIL CONFIGURATION ===
mail.smtp.host=smtp.gmail.com
mail.smtp.port=587
mail.smtp.auth=true
mail.smtp.starttls.enable=true
mail.smtp.user=<EMAIL>
mail.smtp.password=your-email-password
mail.from.address=<EMAIL>
mail.from.name=WebPublication System

# === INTERNATIONALIZATION ===
i18n.default.locale=pt_BR
i18n.supported.locales=pt_BR,en_US
i18n.encoding=UTF-8
i18n.fallback.locale=pt_BR

# === PERFORMANCE ===
performance.monitoring.enabled=true
performance.slow.query.threshold=5000
performance.cache.statistics=true

# === DEVELOPMENT SETTINGS ===
dev.mode.enabled=true
dev.auto.reload=true
dev.show.sql=false
dev.format.sql=true

# === PRODUCTION SETTINGS ===
prod.compression.enabled=true
prod.minify.resources=true
prod.cache.static.resources=true
prod.error.page.detailed=false
```

### **2. database.properties**

#### **Configurações Específicas de Banco:**

```properties
# ========================================
# Database Configuration Properties
# ========================================

# === ORACLE CRM DATABASE ===
oracle.crm.driver=oracle.jdbc.OracleDriver
oracle.crm.url=jdbc:oracle:thin:@**************:1521:MORALPDB
oracle.crm.username=WEBP_CRM_DEV
oracle.crm.password=mSKqzKHzdDJk
oracle.crm.schema=WEBP_CRM_DEV
oracle.crm.dialect=org.hibernate.dialect.Oracle12cDialect
oracle.crm.show.sql=false
oracle.crm.format.sql=true
oracle.crm.hbm2ddl.auto=validate

# === MYSQL VISIONNAIRE DATABASE ===
mysql.visionnaire.driver=com.mysql.cj.jdbc.Driver
mysql.visionnaire.url=*****************************************************************************************************************************************************
mysql.visionnaire.username=root
mysql.visionnaire.password=a4DqYcBvKB
mysql.visionnaire.schema=dev_visionnaire
mysql.visionnaire.dialect=org.hibernate.dialect.MySQL8Dialect
mysql.visionnaire.show.sql=false

# === SQL SERVER FIEPE DATABASE ===
sqlserver.fiepe.driver=com.microsoft.sqlserver.jdbc.SQLServerDriver
sqlserver.fiepe.url=*********************************************************************************************************************
sqlserver.fiepe.username=sa
sqlserver.fiepe.password=a4DqYcBvKB
sqlserver.fiepe.schema=dbo
sqlserver.fiepe.dialect=org.hibernate.dialect.SQLServer2012Dialect

# === POSTGRESQL CITS DATABASE ===
postgresql.cits.driver=org.postgresql.Driver
postgresql.cits.url=********************************************************************************************
postgresql.cits.username=postgres
postgresql.cits.password=a4DqYcBvKB
postgresql.cits.schema=public
postgresql.cits.dialect=org.hibernate.dialect.PostgreSQL95Dialect

# === CONNECTION POOL SETTINGS ===
db.pool.initial.size=5
db.pool.max.size=20
db.pool.min.idle=5
db.pool.max.idle=10
db.pool.max.wait=10000
db.pool.validation.query=SELECT 1
db.pool.test.on.borrow=true
db.pool.test.while.idle=true
db.pool.time.between.eviction.runs=30000
db.pool.min.evictable.idle.time=30000
db.pool.remove.abandoned=true
db.pool.remove.abandoned.timeout=60
db.pool.log.abandoned=true

# === TRANSACTION SETTINGS ===
transaction.timeout=300
transaction.isolation=READ_COMMITTED
transaction.auto.commit=false
```

### **3. log4j2.xml (Migração do log4j.properties)**

#### **Nova Configuração Log4j 2:**

```xml
<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN" monitorInterval="30">
    
    <!-- Properties -->
    <Properties>
        <Property name="LOG_PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n</Property>
        <Property name="LOG_PATH">${sys:catalina.base}/logs</Property>
        <Property name="APP_NAME">webpublication</Property>
    </Properties>
    
    <!-- Appenders -->
    <Appenders>
        
        <!-- Console Appender -->
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
        </Console>
        
        <!-- Application Log File -->
        <RollingFile name="ApplicationLog" 
                     fileName="${LOG_PATH}/${APP_NAME}.log"
                     filePattern="${LOG_PATH}/${APP_NAME}-%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="100MB"/>
            </Policies>
            <DefaultRolloverStrategy max="30"/>
        </RollingFile>
        
        <!-- Error Log File -->
        <RollingFile name="ErrorLog" 
                     fileName="${LOG_PATH}/${APP_NAME}-error.log"
                     filePattern="${LOG_PATH}/${APP_NAME}-error-%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="50MB"/>
            </Policies>
            <DefaultRolloverStrategy max="30"/>
        </RollingFile>
        
        <!-- Database Log File -->
        <RollingFile name="DatabaseLog" 
                     fileName="${LOG_PATH}/${APP_NAME}-database.log"
                     filePattern="${LOG_PATH}/${APP_NAME}-database-%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="50MB"/>
            </Policies>
            <DefaultRolloverStrategy max="15"/>
        </RollingFile>
        
        <!-- Performance Log File -->
        <RollingFile name="PerformanceLog" 
                     fileName="${LOG_PATH}/${APP_NAME}-performance.log"
                     filePattern="${LOG_PATH}/${APP_NAME}-performance-%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %msg%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="50MB"/>
            </Policies>
            <DefaultRolloverStrategy max="15"/>
        </RollingFile>
        
    </Appenders>
    
    <!-- Loggers -->
    <Loggers>
        
        <!-- Application Loggers -->
        <Logger name="com.visionnaire.webpublication" level="DEBUG" additivity="false">
            <AppenderRef ref="ApplicationLog"/>
            <AppenderRef ref="ErrorLog"/>
            <AppenderRef ref="Console"/>
        </Logger>
        
        <!-- PSS Framework -->
        <Logger name="com.visionnaire.PSS" level="INFO" additivity="false">
            <AppenderRef ref="ApplicationLog"/>
            <AppenderRef ref="ErrorLog"/>
        </Logger>
        
        <!-- Database Loggers -->
        <Logger name="org.hibernate" level="WARN" additivity="false">
            <AppenderRef ref="DatabaseLog"/>
        </Logger>
        
        <Logger name="org.hibernate.SQL" level="DEBUG" additivity="false">
            <AppenderRef ref="DatabaseLog"/>
        </Logger>
        
        <Logger name="org.hibernate.type.descriptor.sql.BasicBinder" level="TRACE" additivity="false">
            <AppenderRef ref="DatabaseLog"/>
        </Logger>
        
        <!-- Connection Pool -->
        <Logger name="org.apache.tomcat.jdbc.pool" level="INFO" additivity="false">
            <AppenderRef ref="DatabaseLog"/>
        </Logger>
        
        <!-- Framework Loggers -->
        <Logger name="org.apache.tiles" level="WARN" additivity="false">
            <AppenderRef ref="ApplicationLog"/>
        </Logger>
        
        <Logger name="org.directwebremoting" level="WARN" additivity="false">
            <AppenderRef ref="ApplicationLog"/>
        </Logger>
        
        <Logger name="org.springframework" level="INFO" additivity="false">
            <AppenderRef ref="ApplicationLog"/>
        </Logger>
        
        <!-- Performance Logger -->
        <Logger name="performance" level="INFO" additivity="false">
            <AppenderRef ref="PerformanceLog"/>
        </Logger>
        
        <!-- Third-party Libraries -->
        <Logger name="org.apache.commons" level="WARN"/>
        <Logger name="com.fasterxml.jackson" level="WARN"/>
        <Logger name="com.google.gson" level="WARN"/>
        
        <!-- Root Logger -->
        <Root level="INFO">
            <AppenderRef ref="ApplicationLog"/>
            <AppenderRef ref="ErrorLog"/>
            <AppenderRef ref="Console"/>
        </Root>
        
    </Loggers>
    
</Configuration>
```

### **4. messages.properties (Internacionalização)**

#### **messages_pt_BR.properties:**

```properties
# ========================================
# Mensagens da Aplicação - Português Brasil
# Encoding: UTF-8
# ========================================

# === MENSAGENS GERAIS ===
app.title=WebPublication - Sistema de Publicação
app.welcome=Bem-vindo ao WebPublication
app.version=Versão 2.0 - Java 21

# === NAVEGAÇÃO ===
nav.home=Início
nav.publications=Publicações
nav.users=Usuários
nav.reports=Relatórios
nav.admin=Administração
nav.logout=Sair

# === FORMULÁRIOS ===
form.save=Salvar
form.cancel=Cancelar
form.edit=Editar
form.delete=Excluir
form.search=Pesquisar
form.clear=Limpar
form.submit=Enviar
form.reset=Resetar

# === VALIDAÇÃO ===
validation.required=Campo obrigatório
validation.email.invalid=Email inválido
validation.date.invalid=Data inválida
validation.number.invalid=Número inválido
validation.file.size.exceeded=Arquivo muito grande (máximo {0} MB)
validation.file.type.invalid=Tipo de arquivo não permitido

# === MENSAGENS DE SUCESSO ===
success.save=Registro salvo com sucesso
success.update=Registro atualizado com sucesso
success.delete=Registro excluído com sucesso
success.upload=Arquivo enviado com sucesso

# === MENSAGENS DE ERRO ===
error.general=Ocorreu um erro inesperado
error.database=Erro de conexão com banco de dados
error.permission.denied=Acesso negado
error.file.not.found=Arquivo não encontrado
error.session.expired=Sessão expirada

# === PUBLICAÇÕES ===
publication.title=Título da Publicação
publication.author=Autor
publication.date=Data de Publicação
publication.status=Status
publication.category=Categoria
publication.description=Descrição

# === USUÁRIOS ===
user.name=Nome
user.email=Email
user.role=Perfil
user.active=Ativo
user.created.date=Data de Criação
user.last.login=Último Acesso

# === RELATÓRIOS ===
report.publications.by.date=Publicações por Data
report.users.activity=Atividade dos Usuários
report.system.statistics=Estatísticas do Sistema
report.generate=Gerar Relatório
report.export.pdf=Exportar PDF
report.export.excel=Exportar Excel

# === SISTEMA ===
system.maintenance=Sistema em manutenção
system.backup.completed=Backup concluído
system.cache.cleared=Cache limpo
system.database.connected=Conectado ao banco de dados
system.java.version=Java {0}
system.server.info=Servidor: {0}
```

### **5. tiles.xml (Atualizado para Tiles 3.0)**

```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE tiles-definitions PUBLIC
    "-//Apache Software Foundation//DTD Tiles Configuration 3.0//EN"
    "http://tiles.apache.org/dtds/tiles-config_3_0.dtd">

<tiles-definitions>
    
    <!-- Base Definition -->
    <definition name="base.definition" template="/WEB-INF/jsp/layout/base.jsp">
        <put-attribute name="title" value="WebPublication"/>
        <put-attribute name="meta" value="/WEB-INF/jsp/layout/meta.jsp"/>
        <put-attribute name="header" value="/WEB-INF/jsp/layout/header.jsp"/>
        <put-attribute name="navigation" value="/WEB-INF/jsp/layout/navigation.jsp"/>
        <put-attribute name="sidebar" value="/WEB-INF/jsp/layout/sidebar.jsp"/>
        <put-attribute name="content" value=""/>
        <put-attribute name="footer" value="/WEB-INF/jsp/layout/footer.jsp"/>
        <put-attribute name="scripts" value="/WEB-INF/jsp/layout/scripts.jsp"/>
    </definition>
    
    <!-- Public Layout -->
    <definition name="public.definition" template="/WEB-INF/jsp/layout/public.jsp">
        <put-attribute name="title" value="WebPublication"/>
        <put-attribute name="meta" value="/WEB-INF/jsp/layout/meta.jsp"/>
        <put-attribute name="header" value="/WEB-INF/jsp/layout/public-header.jsp"/>
        <put-attribute name="content" value=""/>
        <put-attribute name="footer" value="/WEB-INF/jsp/layout/public-footer.jsp"/>
        <put-attribute name="scripts" value="/WEB-INF/jsp/layout/scripts.jsp"/>
    </definition>
    
    <!-- Admin Layout -->
    <definition name="admin.definition" extends="base.definition">
        <put-attribute name="title" value="WebPublication - Administração"/>
        <put-attribute name="navigation" value="/WEB-INF/jsp/layout/admin-navigation.jsp"/>
        <put-attribute name="sidebar" value="/WEB-INF/jsp/layout/admin-sidebar.jsp"/>
    </definition>
    
    <!-- Home Pages -->
    <definition name="home" extends="base.definition">
        <put-attribute name="title" value="WebPublication - Início"/>
        <put-attribute name="content" value="/WEB-INF/jsp/home/<USER>"/>
    </definition>
    
    <!-- Publication Pages -->
    <definition name="publications.list" extends="base.definition">
        <put-attribute name="title" value="WebPublication - Publicações"/>
        <put-attribute name="content" value="/WEB-INF/jsp/publications/list.jsp"/>
    </definition>
    
    <definition name="publications.form" extends="base.definition">
        <put-attribute name="title" value="WebPublication - Editar Publicação"/>
        <put-attribute name="content" value="/WEB-INF/jsp/publications/form.jsp"/>
    </definition>
    
    <!-- User Pages -->
    <definition name="users.list" extends="admin.definition">
        <put-attribute name="title" value="WebPublication - Usuários"/>
        <put-attribute name="content" value="/WEB-INF/jsp/users/list.jsp"/>
    </definition>
    
    <!-- Error Pages -->
    <definition name="error.404" extends="public.definition">
        <put-attribute name="title" value="Página não encontrada"/>
        <put-attribute name="content" value="/WEB-INF/jsp/error/404.jsp"/>
    </definition>
    
    <definition name="error.500" extends="public.definition">
        <put-attribute name="title" value="Erro interno do servidor"/>
        <put-attribute name="content" value="/WEB-INF/jsp/error/500.jsp"/>
    </definition>
    
</tiles-definitions>
```

## Scripts de Migração

### **migrate_properties.ps1**
```powershell
# Script para migração de propriedades
Write-Host "=== MIGRAÇÃO DE PROPRIEDADES DA APLICAÇÃO ===" -ForegroundColor Yellow

$sourceDir = "Source\WEB-INF\classes"
$backupDir = "backup\properties"

# Criar backup
New-Item -ItemType Directory -Force -Path $backupDir
Copy-Item "$sourceDir\*.properties" $backupDir -ErrorAction SilentlyContinue

# Verificar arquivos de propriedades existentes
$propertyFiles = Get-ChildItem "$sourceDir\*.properties" -ErrorAction SilentlyContinue

Write-Host "Arquivos de propriedades encontrados:" -ForegroundColor Cyan
foreach ($file in $propertyFiles) {
    Write-Host "  - $($file.Name)" -ForegroundColor White
    
    # Verificar encoding
    $content = Get-Content $file.FullName -Raw
    if ($content -match '[^\x00-\x7F]') {
        Write-Host "    ⚠️ Contém caracteres não-ASCII - verificar encoding" -ForegroundColor Yellow
    }
}

# Migrar log4j.properties para log4j2.xml
if (Test-Path "$sourceDir\log4j.properties") {
    Write-Host ""
    Write-Host "⚠️ log4j.properties encontrado - migração para log4j2.xml necessária" -ForegroundColor Yellow
    Write-Host "1. Backup criado em: $backupDir\log4j.properties" -ForegroundColor Cyan
    Write-Host "2. Criar log4j2.xml conforme documentação" -ForegroundColor Cyan
    Write-Host "3. Remover log4j.properties após validação" -ForegroundColor Cyan
}

# Verificar configurações de banco
Write-Host ""
Write-Host "=== VERIFICANDO CONFIGURAÇÕES DE BANCO ===" -ForegroundColor Cyan

$dbProperties = @("database.properties", "application.properties")
foreach ($dbProp in $dbProperties) {
    if (Test-Path "$sourceDir\$dbProp") {
        $content = Get-Content "$sourceDir\$dbProp" -Raw
        
        # Verificar drivers antigos
        if ($content -match "mysql-connector-java-5") {
            Write-Host "⚠️ $dbProp: Driver MySQL antigo detectado" -ForegroundColor Yellow
        }
        
        if ($content -match "ojdbc[678]") {
            Write-Host "⚠️ $dbProp: Driver Oracle antigo detectado" -ForegroundColor Yellow
        }
        
        # Verificar encoding
        if ($content -notmatch "UTF-8" -and $content -notmatch "characterEncoding") {
            Write-Host "⚠️ $dbProp: Configuração de encoding UTF-8 não encontrada" -ForegroundColor Yellow
        }
    }
}

Write-Host ""
Write-Host "✅ Análise de propriedades concluída" -ForegroundColor Green
```

### **update_properties.bat**
```batch
@echo off
echo ========================================
echo    ATUALIZAÇÃO DE PROPRIEDADES
echo ========================================
echo.

set SOURCE_DIR=Source\WEB-INF\classes
set CONFIG_DIR=config\properties

echo === BACKUP DAS PROPRIEDADES ATUAIS ===
mkdir "backup\properties" 2>nul
copy "%SOURCE_DIR%\*.properties" "backup\properties\" 2>nul
copy "%SOURCE_DIR%\*.xml" "backup\properties\" 2>nul
echo ✅ Backup criado

echo.
echo === APLICANDO NOVAS CONFIGURAÇÕES ===

REM Aplicar propriedades atualizadas
if exist "%CONFIG_DIR%\application.properties" (
    copy "%CONFIG_DIR%\application.properties" "%SOURCE_DIR%\"
    echo ✅ application.properties atualizado
)

if exist "%CONFIG_DIR%\database.properties" (
    copy "%CONFIG_DIR%\database.properties" "%SOURCE_DIR%\"
    echo ✅ database.properties atualizado
)

if exist "%CONFIG_DIR%\log4j2.xml" (
    copy "%CONFIG_DIR%\log4j2.xml" "%SOURCE_DIR%\"
    echo ✅ log4j2.xml instalado
)

if exist "%CONFIG_DIR%\messages_pt_BR.properties" (
    copy "%CONFIG_DIR%\messages_pt_BR.properties" "%SOURCE_DIR%\"
    echo ✅ messages_pt_BR.properties atualizado
)

REM Remover configurações antigas
del "%SOURCE_DIR%\log4j.properties" 2>nul
echo ✅ log4j.properties removido (migrado para log4j2.xml)

echo.
echo === VERIFICAÇÃO ===
echo Propriedades atuais:
dir "%SOURCE_DIR%\*.properties" 2>nul
dir "%SOURCE_DIR%\log4j2.xml" 2>nul

echo.
echo ✅ Atualização de propriedades concluída!
pause
```

## Validação e Testes

### **test_properties.java**
```java
import java.io.*;
import java.util.Properties;

public class TestProperties {
    
    public static void main(String[] args) {
        System.out.println("=== TESTE DE PROPRIEDADES DA APLICAÇÃO ===");
        System.out.println();
        
        testApplicationProperties();
        testDatabaseProperties();
        testMessagesProperties();
        testLog4j2Configuration();
    }
    
    private static void testApplicationProperties() {
        System.out.println("=== TESTANDO application.properties ===");
        try {
            Properties props = new Properties();
            props.load(TestProperties.class.getResourceAsStream("/application.properties"));
            
            // Verificar propriedades essenciais
            String appName = props.getProperty("app.name");
            String javaVersion = props.getProperty("app.java.version");
            String environment = props.getProperty("app.environment");
            
            System.out.println("✅ Aplicação: " + appName);
            System.out.println("✅ Java Version: " + javaVersion);
            System.out.println("✅ Environment: " + environment);
            
            // Verificar configurações de banco
            String oracleJndi = props.getProperty("db.oracle.jndi.name");
            String mysqlJndi = props.getProperty("db.mysql.jndi.name");
            
            if (oracleJndi != null) System.out.println("✅ Oracle JNDI: " + oracleJndi);
            if (mysqlJndi != null) System.out.println("✅ MySQL JNDI: " + mysqlJndi);
            
        } catch (Exception e) {
            System.out.println("❌ Erro ao carregar application.properties: " + e.getMessage());
        }
        System.out.println();
    }
    
    private static void testDatabaseProperties() {
        System.out.println("=== TESTANDO database.properties ===");
        try {
            Properties props = new Properties();
            props.load(TestProperties.class.getResourceAsStream("/database.properties"));
            
            // Verificar drivers
            String oracleDriver = props.getProperty("oracle.crm.driver");
            String mysqlDriver = props.getProperty("mysql.visionnaire.driver");
            
            if (oracleDriver != null) {
                System.out.println("✅ Oracle Driver: " + oracleDriver);
                // Verificar se driver está disponível
                try {
                    Class.forName(oracleDriver);
                    System.out.println("✅ Oracle Driver carregado com sucesso");
                } catch (ClassNotFoundException e) {
                    System.out.println("❌ Oracle Driver não encontrado no classpath");
                }
            }
            
            if (mysqlDriver != null) {
                System.out.println("✅ MySQL Driver: " + mysqlDriver);
                try {
                    Class.forName(mysqlDriver);
                    System.out.println("✅ MySQL Driver carregado com sucesso");
                } catch (ClassNotFoundException e) {
                    System.out.println("❌ MySQL Driver não encontrado no classpath");
                }
            }
            
        } catch (Exception e) {
            System.out.println("❌ Erro ao carregar database.properties: " + e.getMessage());
        }
        System.out.println();
    }
    
    private static void testMessagesProperties() {
        System.out.println("=== TESTANDO messages.properties ===");
        try {
            Properties props = new Properties();
            props.load(TestProperties.class.getResourceAsStream("/messages_pt_BR.properties"));
            
            String appTitle = props.getProperty("app.title");
            String welcome = props.getProperty("app.welcome");
            
            System.out.println("✅ App Title: " + appTitle);
            System.out.println("✅ Welcome: " + welcome);
            System.out.println("✅ Total de mensagens: " + props.size());
            
        } catch (Exception e) {
            System.out.println("❌ Erro ao carregar messages_pt_BR.properties: " + e.getMessage());
        }
        System.out.println();
    }
    
    private static void testLog4j2Configuration() {
        System.out.println("=== TESTANDO log4j2.xml ===");
        try {
            InputStream is = TestProperties.class.getResourceAsStream("/log4j2.xml");
            if (is != null) {
                System.out.println("✅ log4j2.xml encontrado");
                
                // Verificar se Log4j 2 está funcionando
                org.apache.logging.log4j.Logger logger = 
                    org.apache.logging.log4j.LogManager.getLogger(TestProperties.class);
                logger.info("Teste de log4j2 - configuração funcionando");
                System.out.println("✅ Log4j 2 funcionando corretamente");
                
            } else {
                System.out.println("❌ log4j2.xml não encontrado");
            }
        } catch (Exception e) {
            System.out.println("❌ Erro ao testar log4j2: " + e.getMessage());
        }
        System.out.println();
    }
}
```

## Checklist de Validação

### **Arquivos de Propriedades:**
- [ ] application.properties atualizado para Java 21
- [ ] database.properties com drivers atualizados
- [ ] messages_pt_BR.properties com encoding UTF-8
- [ ] log4j.properties migrado para log4j2.xml
- [ ] tiles.xml atualizado para Tiles 3.0

### **Configurações de Banco:**
- [ ] JNDI names corretos configurados
- [ ] Drivers atualizados referenciados
- [ ] URLs de conexão com parâmetros Java 21
- [ ] Pool de conexões otimizado
- [ ] Encoding UTF-8 configurado

### **Internacionalização:**
- [ ] Mensagens em português atualizadas
- [ ] Encoding UTF-8 em todos os arquivos
- [ ] Fallback locale configurado
- [ ] Suporte a múltiplos idiomas

### **Logging:**
- [ ] Log4j 2 configurado corretamente
- [ ] Appenders configurados
- [ ] Níveis de log apropriados
- [ ] Rotação de logs configurada
- [ ] Performance logging habilitado

### **Segurança:**
- [ ] Senhas não expostas em propriedades
- [ ] Configurações de segurança atualizadas
- [ ] CSRF protection configurado
- [ ] Session security configurada

## Próximo Passo
**[4.4 Configurações de Build](./4.4-configuracoes-build.md)**

---
**Status**: ⏳ Pendente
**Responsável**: Desenvolvedor Senior
**Estimativa**: 2-3 dias
