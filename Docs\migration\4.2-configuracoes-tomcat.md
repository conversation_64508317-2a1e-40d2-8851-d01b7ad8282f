# 4.2 Configurações do Tomcat

## Objetivo
Configurar o Apache Tomcat 10.1.15 para otimizar performance, segurança e compatibilidade com Java 21 e Jakarta EE.

## Pré-requisitos
- Tomcat 10.1.15 instalado (2.2)
- web.xml atualizado para Servlet 5.0 (4.1)
- JDK 21 configurado

## Tempo Estimado
1-2 dias

## Configurações Principais

### **Arquivos de Configuração do Tomcat:**

#### **1. server.xml** - Configuração principal do servidor
#### **2. context.xml** - Configuração de contexto e DataSources
#### **3. tomcat-users.xml** - Usuários e roles
#### **4. logging.properties** - Configuração de logs
#### **5. setenv.bat** - Variáveis de ambiente e JVM
#### **6. web.xml** - Configuração global de aplicações

## Configuração Detalhada

### **1. server.xml - Configuração Principal**

#### **Localização**: `%CATALINA_HOME%\conf\server.xml`

```xml
<?xml version="1.0" encoding="UTF-8"?>
<Server port="8005" shutdown="SHUTDOWN">
  <!-- Listeners para inicialização -->
  <Listener className="org.apache.catalina.startup.VersionLoggerListener" />
  <Listener className="org.apache.catalina.core.AprLifecycleListener" SSLEngine="on" />
  <Listener className="org.apache.catalina.core.JreMemoryLeakPreventionListener" />
  <Listener className="org.apache.catalina.mbeans.GlobalResourcesLifecycleListener" />
  <Listener className="org.apache.catalina.core.ThreadLocalLeakPreventionListener" />

  <!-- Global JNDI resources -->
  <GlobalNamingResources>
    <Resource name="UserDatabase" auth="Container"
              type="org.apache.catalina.UserDatabase"
              description="User database that can be updated and saved"
              factory="org.apache.catalina.users.MemoryUserDatabaseFactory"
              pathname="conf/tomcat-users.xml" />
  </GlobalNamingResources>

  <!-- Service principal -->
  <Service name="Catalina">
    
    <!-- Connector HTTP/1.1 -->
    <Connector port="8080" 
               protocol="HTTP/1.1"
               connectionTimeout="20000"
               redirectPort="8443"
               maxParameterCount="1000"
               maxPostSize="2097152"
               maxSwallowSize="2097152"
               maxThreads="200"
               minSpareThreads="10"
               maxSpareThreads="75"
               enableLookups="false"
               disableUploadTimeout="true"
               acceptCount="100"
               compression="on"
               compressionMinSize="2048"
               compressableMimeType="text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json,application/xml"
               URIEncoding="UTF-8" />

    <!-- Connector HTTPS (se necessário) -->
    <!--
    <Connector port="8443" 
               protocol="org.apache.coyote.http11.Http11NioProtocol"
               maxThreads="150" 
               SSLEnabled="true"
               scheme="https" 
               secure="true"
               clientAuth="false" 
               sslProtocol="TLS"
               keystoreFile="conf/keystore.jks"
               keystorePass="changeit"
               keyAlias="tomcat">
        <UpgradeProtocol className="org.apache.coyote.http2.Http2Protocol" />
    </Connector>
    -->

    <!-- Engine principal -->
    <Engine name="Catalina" defaultHost="localhost">

      <!-- Realm para autenticação -->
      <Realm className="org.apache.catalina.realm.LockOutRealm">
        <Realm className="org.apache.catalina.realm.UserDatabaseRealm"
               resourceName="UserDatabase"/>
      </Realm>

      <!-- Host virtual -->
      <Host name="localhost" 
            appBase="webapps"
            unpackWARs="true" 
            autoDeploy="true"
            deployOnStartup="true"
            backgroundProcessorDelay="10">

        <!-- Valve para logs de acesso -->
        <Valve className="org.apache.catalina.valves.AccessLogValve" 
               directory="logs"
               prefix="localhost_access_log" 
               suffix=".txt"
               pattern="%h %l %u %t &quot;%r&quot; %s %b %D"
               rotatable="true"
               maxDays="30" />

        <!-- Valve para compressão (se não configurado no Connector) -->
        <!--
        <Valve className="org.apache.catalina.valves.CompressionValve"
               compression="on"
               compressionMinSize="2048" />
        -->

        <!-- Valve para cache de recursos estáticos -->
        <Valve className="org.apache.catalina.valves.CachedResourcesValve"
               cacheMaxSize="10240"
               cacheTTL="5000" />

      </Host>
    </Engine>
  </Service>
</Server>
```

### **2. context.xml - DataSources e Recursos**

#### **Localização**: `%CATALINA_HOME%\conf\context.xml`

```xml
<?xml version="1.0" encoding="UTF-8"?>
<Context>
    <!-- Prevent memory leaks -->
    <Manager pathname="" />
    
    <!-- Configurações de sessão -->
    <Manager className="org.apache.catalina.session.StandardManager"
             maxActiveSessions="1000"
             sessionIdLength="32" />
    
    <!-- DataSource para Oracle CRM -->
    <Resource name="jdbc/WebpCrmDev"
              auth="Container"
              type="javax.sql.DataSource"
              factory="org.apache.tomcat.jdbc.pool.DataSourceFactory"
              driverClassName="oracle.jdbc.OracleDriver"
              url="**********************************************"
              username="WEBP_CRM_DEV"
              password="mSKqzKHzdDJk"
              
              <!-- Pool Configuration -->
              initialSize="5"
              maxTotal="20"
              maxIdle="10"
              minIdle="5"
              maxWaitMillis="10000"
              
              <!-- Validation -->
              testOnBorrow="true"
              testOnReturn="false"
              testWhileIdle="true"
              validationQuery="SELECT 1 FROM DUAL"
              validationInterval="30000"
              
              <!-- Connection Management -->
              removeAbandoned="true"
              removeAbandonedTimeout="60"
              logAbandoned="true"
              
              <!-- Performance -->
              timeBetweenEvictionRunsMillis="30000"
              minEvictableIdleTimeMillis="30000"
              
              <!-- JDBC Properties -->
              connectionProperties="oracle.net.CONNECT_TIMEOUT=10000;oracle.jdbc.ReadTimeout=30000" />

    <!-- DataSource para MySQL Visionnaire -->
    <Resource name="jdbc/VisionnaireDevMysql"
              auth="Container"
              type="javax.sql.DataSource"
              factory="org.apache.tomcat.jdbc.pool.DataSourceFactory"
              driverClassName="com.mysql.cj.jdbc.Driver"
              url="*********************************************************************************************************************************************************************"
              username="root"
              password="a4DqYcBvKB"
              
              <!-- Pool Configuration -->
              initialSize="3"
              maxTotal="15"
              maxIdle="8"
              minIdle="3"
              maxWaitMillis="10000"
              
              <!-- Validation -->
              testOnBorrow="true"
              testWhileIdle="true"
              validationQuery="SELECT 1"
              validationInterval="30000"
              
              <!-- Connection Management -->
              removeAbandoned="true"
              removeAbandonedTimeout="60"
              
              <!-- MySQL Specific -->
              connectionProperties="autoReconnect=true;failOverReadOnly=false;maxReconnects=10" />

    <!-- DataSource para SQL Server FIEPE -->
    <Resource name="jdbc/FiepeDevSqlserver"
              auth="Container"
              type="javax.sql.DataSource"
              factory="org.apache.tomcat.jdbc.pool.DataSourceFactory"
              driverClassName="com.microsoft.sqlserver.jdbc.SQLServerDriver"
              url="*********************************************************************************************************************"
              username="sa"
              password="a4DqYcBvKB"
              
              <!-- Pool Configuration -->
              initialSize="3"
              maxTotal="15"
              maxIdle="8"
              minIdle="3"
              maxWaitMillis="10000"
              
              <!-- Validation -->
              testOnBorrow="true"
              testWhileIdle="true"
              validationQuery="SELECT 1"
              validationInterval="30000"
              
              <!-- Connection Management -->
              removeAbandoned="true"
              removeAbandonedTimeout="60" />

    <!-- DataSource para PostgreSQL CITS -->
    <Resource name="jdbc/CitsProdPostgresql"
              auth="Container"
              type="javax.sql.DataSource"
              factory="org.apache.tomcat.jdbc.pool.DataSourceFactory"
              driverClassName="org.postgresql.Driver"
              url="****************************************************************************************************"
              username="postgres"
              password="a4DqYcBvKB"
              
              <!-- Pool Configuration -->
              initialSize="3"
              maxTotal="15"
              maxIdle="8"
              minIdle="3"
              maxWaitMillis="10000"
              
              <!-- Validation -->
              testOnBorrow="true"
              testWhileIdle="true"
              validationQuery="SELECT 1"
              validationInterval="30000"
              
              <!-- Connection Management -->
              removeAbandoned="true"
              removeAbandonedTimeout="60" />

    <!-- Mail Session (se necessário) -->
    <Resource name="mail/Session"
              auth="Container"
              type="jakarta.mail.Session"
              mail.smtp.host="smtp.gmail.com"
              mail.smtp.port="587"
              mail.smtp.auth="true"
              mail.smtp.starttls.enable="true"
              mail.smtp.user="<EMAIL>"
              password="your-email-password" />

    <!-- Environment Variables -->
    <Environment name="app.environment" value="development" type="java.lang.String"/>
    <Environment name="app.version" value="1.0" type="java.lang.String"/>
    
    <!-- Resource Links (se necessário) -->
    <ResourceLink name="jdbc/WebpCrmDev" global="jdbc/WebpCrmDev" type="javax.sql.DataSource"/>
    
</Context>
```

### **3. setenv.bat - Configurações JVM**

#### **Localização**: `%CATALINA_HOME%\bin\setenv.bat`

```batch
@echo off
rem ========================================
rem Configurações JVM para Tomcat 10.1.15 + Java 21
rem ========================================

rem === JAVA CONFIGURATION ===
set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot
set JRE_HOME=%JAVA_HOME%

rem === MEMORY SETTINGS ===
set CATALINA_OPTS=%CATALINA_OPTS% -Xms1024m
set CATALINA_OPTS=%CATALINA_OPTS% -Xmx4096m
set CATALINA_OPTS=%CATALINA_OPTS% -XX:MetaspaceSize=256m
set CATALINA_OPTS=%CATALINA_OPTS% -XX:MaxMetaspaceSize=512m
set CATALINA_OPTS=%CATALINA_OPTS% -XX:CompressedClassSpaceSize=128m

rem === GARBAGE COLLECTOR ===
set CATALINA_OPTS=%CATALINA_OPTS% -XX:+UseG1GC
set CATALINA_OPTS=%CATALINA_OPTS% -XX:MaxGCPauseMillis=200
set CATALINA_OPTS=%CATALINA_OPTS% -XX:+UseStringDeduplication
set CATALINA_OPTS=%CATALINA_OPTS% -XX:G1HeapRegionSize=16m
set CATALINA_OPTS=%CATALINA_OPTS% -XX:G1NewSizePercent=30
set CATALINA_OPTS=%CATALINA_OPTS% -XX:G1MaxNewSizePercent=40

rem === JIT COMPILER ===
set CATALINA_OPTS=%CATALINA_OPTS% -XX:+TieredCompilation
set CATALINA_OPTS=%CATALINA_OPTS% -XX:TieredStopAtLevel=4

rem === MODULE SYSTEM COMPATIBILITY ===
set CATALINA_OPTS=%CATALINA_OPTS% --add-opens java.base/java.lang=ALL-UNNAMED
set CATALINA_OPTS=%CATALINA_OPTS% --add-opens java.base/java.util=ALL-UNNAMED
set CATALINA_OPTS=%CATALINA_OPTS% --add-opens java.base/java.util.concurrent=ALL-UNNAMED
set CATALINA_OPTS=%CATALINA_OPTS% --add-opens java.base/java.util.concurrent.locks=ALL-UNNAMED
set CATALINA_OPTS=%CATALINA_OPTS% --add-opens java.base/java.net=ALL-UNNAMED
set CATALINA_OPTS=%CATALINA_OPTS% --add-opens java.base/java.io=ALL-UNNAMED
set CATALINA_OPTS=%CATALINA_OPTS% --add-opens java.base/java.nio=ALL-UNNAMED
set CATALINA_OPTS=%CATALINA_OPTS% --add-opens java.base/sun.nio.ch=ALL-UNNAMED
set CATALINA_OPTS=%CATALINA_OPTS% --add-opens java.rmi/sun.rmi.transport=ALL-UNNAMED
set CATALINA_OPTS=%CATALINA_OPTS% --add-opens java.naming/com.sun.jndi.ldap=ALL-UNNAMED

rem === SECURITY ===
set CATALINA_OPTS=%CATALINA_OPTS% -Djava.security.egd=file:/dev/./urandom
set CATALINA_OPTS=%CATALINA_OPTS% -Djava.awt.headless=true

rem === NETWORK ===
set CATALINA_OPTS=%CATALINA_OPTS% -Djava.net.preferIPv4Stack=true
set CATALINA_OPTS=%CATALINA_OPTS% -Djava.net.useSystemProxies=true

rem === ENCODING ===
set CATALINA_OPTS=%CATALINA_OPTS% -Dfile.encoding=UTF-8
set CATALINA_OPTS=%CATALINA_OPTS% -Dsun.jnu.encoding=UTF-8
set CATALINA_OPTS=%CATALINA_OPTS% -Duser.timezone=America/Sao_Paulo

rem === TOMCAT SPECIFIC ===
set CATALINA_OPTS=%CATALINA_OPTS% -Dorg.apache.catalina.startup.EXIT_ON_INIT_FAILURE=true
set CATALINA_OPTS=%CATALINA_OPTS% -Dorg.apache.tomcat.util.buf.UDecoder.ALLOW_ENCODED_SLASH=true
set CATALINA_OPTS=%CATALINA_OPTS% -Dorg.apache.tomcat.util.http.parser.HttpParser.requestTargetAllow=|

rem === APPLICATION SPECIFIC ===
set CATALINA_OPTS=%CATALINA_OPTS% -Dwebpublication.environment=development
set CATALINA_OPTS=%CATALINA_OPTS% -Dwebpublication.config.path=%CATALINA_HOME%\conf\webpublication
set CATALINA_OPTS=%CATALINA_OPTS% -Dpss.home=V:\Visionnaire\PSS\Dist-3.0

rem === LOGGING ===
set CATALINA_OPTS=%CATALINA_OPTS% -Djava.util.logging.config.file=%CATALINA_HOME%\conf\logging.properties
set CATALINA_OPTS=%CATALINA_OPTS% -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager

rem === DEBUG (apenas desenvolvimento) ===
rem set CATALINA_OPTS=%CATALINA_OPTS% -Xdebug
rem set CATALINA_OPTS=%CATALINA_OPTS% -Xrunjdwp:transport=dt_socket,address=8000,server=y,suspend=n

rem === PROFILING (apenas desenvolvimento) ===
rem set CATALINA_OPTS=%CATALINA_OPTS% -XX:+FlightRecorder
rem set CATALINA_OPTS=%CATALINA_OPTS% -XX:StartFlightRecording=duration=60s,filename=tomcat-startup.jfr

rem === MONITORING ===
set CATALINA_OPTS=%CATALINA_OPTS% -Dcom.sun.management.jmxremote
set CATALINA_OPTS=%CATALINA_OPTS% -Dcom.sun.management.jmxremote.port=9999
set CATALINA_OPTS=%CATALINA_OPTS% -Dcom.sun.management.jmxremote.authenticate=false
set CATALINA_OPTS=%CATALINA_OPTS% -Dcom.sun.management.jmxremote.ssl=false

echo ========================================
echo Configurações CATALINA_OPTS aplicadas:
echo %CATALINA_OPTS%
echo ========================================
```

### **4. tomcat-users.xml - Usuários e Roles**

#### **Localização**: `%CATALINA_HOME%\conf\tomcat-users.xml`

```xml
<?xml version="1.0" encoding="UTF-8"?>
<tomcat-users xmlns="http://tomcat.apache.org/xml"
              xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
              xsi:schemaLocation="http://tomcat.apache.org/xml tomcat-users.xsd"
              version="1.0">

  <!-- Roles para administração -->
  <role rolename="manager-gui"/>
  <role rolename="manager-script"/>
  <role rolename="manager-jmx"/>
  <role rolename="manager-status"/>
  <role rolename="admin-gui"/>
  <role rolename="admin-script"/>
  
  <!-- Roles para aplicação -->
  <role rolename="webpublication-admin"/>
  <role rolename="webpublication-user"/>
  
  <!-- Usuários administrativos -->
  <user username="admin" 
        password="{sha256}240be518fabd2724ddb6f04eeb1da5967448d7e831c08c8fa822809f74c720a9"
        roles="manager-gui,admin-gui,webpublication-admin"/>
        
  <user username="deployer" 
        password="{sha256}ef92b778bafe771e89245b89ecbc08a44a4e166c06659911881f383d4473e94f"
        roles="manager-script,manager-jmx"/>
        
  <user username="monitor" 
        password="{sha256}5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5"
        roles="manager-status"/>

  <!-- Usuários da aplicação (exemplo) -->
  <user username="webpub_admin" 
        password="{sha256}8c6976e5b5410415bde908bd4dee15dfb167a9c873fc4bb8a81f6f2ab448a918"
        roles="webpublication-admin"/>
        
  <user username="webpub_user" 
        password="{sha256}04f8996da763b7a969b1028ee3007569eaf3a635486ddab211d512c85b9df8fb"
        roles="webpublication-user"/>

</tomcat-users>
```

### **5. logging.properties - Configuração de Logs**

#### **Localização**: `%CATALINA_HOME%\conf\logging.properties`

```properties
# Configuração de Logging para Tomcat 10.1.15 + Java 21

# Handlers principais
handlers = 1catalina.org.apache.juli.AsyncFileHandler, 2localhost.org.apache.juli.AsyncFileHandler, 3manager.org.apache.juli.AsyncFileHandler, 4host-manager.org.apache.juli.AsyncFileHandler, 5webpublication.org.apache.juli.AsyncFileHandler, java.util.logging.ConsoleHandler

.handlers = 1catalina.org.apache.juli.AsyncFileHandler, java.util.logging.ConsoleHandler

# === CATALINA LOG ===
1catalina.org.apache.juli.AsyncFileHandler.level = INFO
1catalina.org.apache.juli.AsyncFileHandler.directory = ${catalina.base}/logs
1catalina.org.apache.juli.AsyncFileHandler.prefix = catalina.
1catalina.org.apache.juli.AsyncFileHandler.suffix = .log
1catalina.org.apache.juli.AsyncFileHandler.maxDays = 90
1catalina.org.apache.juli.AsyncFileHandler.encoding = UTF-8
1catalina.org.apache.juli.AsyncFileHandler.formatter = org.apache.juli.OneLineFormatter

# === LOCALHOST LOG ===
2localhost.org.apache.juli.AsyncFileHandler.level = INFO
2localhost.org.apache.juli.AsyncFileHandler.directory = ${catalina.base}/logs
2localhost.org.apache.juli.AsyncFileHandler.prefix = localhost.
2localhost.org.apache.juli.AsyncFileHandler.suffix = .log
2localhost.org.apache.juli.AsyncFileHandler.maxDays = 90
2localhost.org.apache.juli.AsyncFileHandler.encoding = UTF-8
2localhost.org.apache.juli.AsyncFileHandler.formatter = org.apache.juli.OneLineFormatter

# === MANAGER LOG ===
3manager.org.apache.juli.AsyncFileHandler.level = INFO
3manager.org.apache.juli.AsyncFileHandler.directory = ${catalina.base}/logs
3manager.org.apache.juli.AsyncFileHandler.prefix = manager.
3manager.org.apache.juli.AsyncFileHandler.suffix = .log
3manager.org.apache.juli.AsyncFileHandler.maxDays = 90
3manager.org.apache.juli.AsyncFileHandler.encoding = UTF-8
3manager.org.apache.juli.AsyncFileHandler.formatter = org.apache.juli.OneLineFormatter

# === HOST-MANAGER LOG ===
4host-manager.org.apache.juli.AsyncFileHandler.level = INFO
4host-manager.org.apache.juli.AsyncFileHandler.directory = ${catalina.base}/logs
4host-manager.org.apache.juli.AsyncFileHandler.prefix = host-manager.
4host-manager.org.apache.juli.AsyncFileHandler.suffix = .log
4host-manager.org.apache.juli.AsyncFileHandler.maxDays = 90
4host-manager.org.apache.juli.AsyncFileHandler.encoding = UTF-8
4host-manager.org.apache.juli.AsyncFileHandler.formatter = org.apache.juli.OneLineFormatter

# === WEBPUBLICATION LOG ===
5webpublication.org.apache.juli.AsyncFileHandler.level = DEBUG
5webpublication.org.apache.juli.AsyncFileHandler.directory = ${catalina.base}/logs
5webpublication.org.apache.juli.AsyncFileHandler.prefix = webpublication.
5webpublication.org.apache.juli.AsyncFileHandler.suffix = .log
5webpublication.org.apache.juli.AsyncFileHandler.maxDays = 30
5webpublication.org.apache.juli.AsyncFileHandler.encoding = UTF-8
5webpublication.org.apache.juli.AsyncFileHandler.formatter = org.apache.juli.OneLineFormatter

# === CONSOLE HANDLER ===
java.util.logging.ConsoleHandler.level = INFO
java.util.logging.ConsoleHandler.formatter = org.apache.juli.OneLineFormatter
java.util.logging.ConsoleHandler.encoding = UTF-8

# === LOGGER LEVELS ===

# Tomcat Core
org.apache.catalina.core.ContainerBase.[Catalina].[localhost].level = INFO
org.apache.catalina.core.ContainerBase.[Catalina].[localhost].handlers = 2localhost.org.apache.juli.AsyncFileHandler

org.apache.catalina.core.ContainerBase.[Catalina].[localhost].[/manager].level = INFO
org.apache.catalina.core.ContainerBase.[Catalina].[localhost].[/manager].handlers = 3manager.org.apache.juli.AsyncFileHandler

org.apache.catalina.core.ContainerBase.[Catalina].[localhost].[/host-manager].level = INFO
org.apache.catalina.core.ContainerBase.[Catalina].[localhost].[/host-manager].handlers = 4host-manager.org.apache.juli.AsyncFileHandler

# WebPublication Application
org.apache.catalina.core.ContainerBase.[Catalina].[localhost].[/webp].level = DEBUG
org.apache.catalina.core.ContainerBase.[Catalina].[localhost].[/webp].handlers = 5webpublication.org.apache.juli.AsyncFileHandler

# Application Specific Loggers
com.visionnaire.webpublication.level = DEBUG
com.visionnaire.webpublication.handlers = 5webpublication.org.apache.juli.AsyncFileHandler

com.visionnaire.PSS.level = INFO
com.visionnaire.PSS.handlers = 5webpublication.org.apache.juli.AsyncFileHandler

# Database Connection Pool
org.apache.tomcat.jdbc.pool.level = INFO

# Framework Loggers
org.apache.tiles.level = WARN
org.directwebremoting.level = WARN
org.apache.commons.level = WARN

# Third-party Libraries
com.fasterxml.jackson.level = WARN
org.apache.logging.log4j.level = INFO

# Security
org.apache.catalina.realm.level = INFO
org.apache.catalina.authenticator.level = INFO

# Performance
org.apache.coyote.level = INFO
org.apache.tomcat.util.net.level = INFO

# Suppress verbose logs
org.apache.catalina.startup.DigesterFactory.level = WARNING
org.apache.catalina.util.LifecycleBase.level = WARNING
org.apache.coyote.http11.Http11NioProtocol.level = WARNING
org.apache.ssi.level = WARNING
org.apache.tomcat.util.scan.level = WARNING
```

### **6. web.xml Global - Configurações Globais**

#### **Localização**: `%CATALINA_HOME%\conf\web.xml`

```xml
<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns="https://jakarta.ee/xml/ns/jakartaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="https://jakarta.ee/xml/ns/jakartaee 
         https://jakarta.ee/xml/ns/jakartaee/web-app_5_0.xsd"
         version="5.0">

  <!-- Display name -->
  <display-name>Apache Tomcat/10.1.15</display-name>
  <description>
     Apache Tomcat/10.1.15 - Jakarta EE 9 Platform
  </description>

  <!-- Default servlet configuration -->
  <servlet>
    <servlet-name>default</servlet-name>
    <servlet-class>org.apache.catalina.servlets.DefaultServlet</servlet-class>
    <init-param>
      <param-name>debug</param-name>
      <param-value>0</param-value>
    </init-param>
    <init-param>
      <param-name>listings</param-name>
      <param-value>false</param-value>
    </init-param>
    <init-param>
      <param-name>gzip</param-name>
      <param-value>true</param-value>
    </init-param>
    <load-on-startup>1</load-on-startup>
  </servlet>

  <!-- JSP servlet configuration -->
  <servlet>
    <servlet-name>jsp</servlet-name>
    <servlet-class>org.apache.jasper.servlet.JspServlet</servlet-class>
    <init-param>
      <param-name>fork</param-name>
      <param-value>false</param-value>
    </init-param>
    <init-param>
      <param-name>xpoweredBy</param-name>
      <param-value>false</param-value>
    </init-param>
    <init-param>
      <param-name>compilerTargetVM</param-name>
      <param-value>21</param-value>
    </init-param>
    <init-param>
      <param-name>compilerSourceVM</param-name>
      <param-value>21</param-value>
    </init-param>
    <load-on-startup>3</load-on-startup>
  </servlet>

  <!-- Servlet mappings -->
  <servlet-mapping>
    <servlet-name>default</servlet-name>
    <url-pattern>/</url-pattern>
  </servlet-mapping>

  <servlet-mapping>
    <servlet-name>jsp</servlet-name>
    <url-pattern>*.jsp</url-pattern>
  </servlet-mapping>

  <servlet-mapping>
    <servlet-name>jsp</servlet-name>
    <url-pattern>*.jspx</url-pattern>
  </servlet-mapping>

  <!-- Session configuration -->
  <session-config>
    <session-timeout>30</session-timeout>
    <cookie-config>
      <http-only>true</http-only>
      <secure>false</secure>
      <same-site>Lax</same-site>
    </cookie-config>
    <tracking-mode>COOKIE</tracking-mode>
  </session-config>

  <!-- MIME type mappings -->
  <mime-mapping>
    <extension>abs</extension>
    <mime-type>audio/x-mpeg</mime-type>
  </mime-mapping>
  
  <!-- Security constraints -->
  <security-constraint>
    <web-resource-collection>
      <web-resource-name>Protected Manager</web-resource-name>
      <url-pattern>/manager/*</url-pattern>
    </web-resource-collection>
    <auth-constraint>
      <role-name>manager-gui</role-name>
    </auth-constraint>
  </security-constraint>

  <!-- Welcome file list -->
  <welcome-file-list>
    <welcome-file>index.html</welcome-file>
    <welcome-file>index.htm</welcome-file>
    <welcome-file>index.jsp</welcome-file>
  </welcome-file-list>

</web-app>
```

## Scripts de Configuração

### **configure_tomcat.bat**
```batch
@echo off
echo ========================================
echo    CONFIGURAÇÃO TOMCAT 10.1.15
echo ========================================
echo.

set TOMCAT_HOME=D:\java\apache-tomcat-10.1.15

echo === BACKUP DAS CONFIGURAÇÕES ATUAIS ===
mkdir "backup\tomcat-configs" 2>nul
copy "%TOMCAT_HOME%\conf\server.xml" "backup\tomcat-configs\server-original.xml" 2>nul
copy "%TOMCAT_HOME%\conf\context.xml" "backup\tomcat-configs\context-original.xml" 2>nul
copy "%TOMCAT_HOME%\conf\tomcat-users.xml" "backup\tomcat-configs\tomcat-users-original.xml" 2>nul
echo ✅ Backup criado

echo.
echo === APLICANDO CONFIGURAÇÕES ===

REM Copiar configurações otimizadas
copy "config\tomcat\server.xml" "%TOMCAT_HOME%\conf\server.xml"
copy "config\tomcat\context.xml" "%TOMCAT_HOME%\conf\context.xml"
copy "config\tomcat\setenv.bat" "%TOMCAT_HOME%\bin\setenv.bat"
copy "config\tomcat\logging.properties" "%TOMCAT_HOME%\conf\logging.properties"

echo ✅ Configurações aplicadas

echo.
echo === CRIANDO DIRETÓRIOS ===
mkdir "%TOMCAT_HOME%\logs" 2>nul
mkdir "%TOMCAT_HOME%\temp" 2>nul
mkdir "%TOMCAT_HOME%\work" 2>nul
mkdir "%TOMCAT_HOME%\conf\webpublication" 2>nul

echo.
echo === CONFIGURANDO PERMISSÕES ===
icacls "%TOMCAT_HOME%" /grant "Users:(OI)(CI)F" /T

echo.
echo === TESTANDO CONFIGURAÇÃO ===
"%TOMCAT_HOME%\bin\configtest.bat"

if %errorlevel% equ 0 (
    echo ✅ Configuração válida
) else (
    echo ❌ Erro na configuração
)

echo.
echo ✅ Configuração do Tomcat concluída!
echo.
echo Próximos passos:
echo 1. Iniciar Tomcat: net start Tomcat10
echo 2. Verificar logs: %TOMCAT_HOME%\logs\catalina.out
echo 3. Acessar manager: http://localhost:8080/manager/html
echo.
pause
```

## Testes de Validação

### **test_tomcat_config.ps1**
```powershell
# Script para testar configurações do Tomcat
Write-Host "=== TESTE DE CONFIGURAÇÕES TOMCAT ===" -ForegroundColor Yellow

$tomcatHome = "D:\java\apache-tomcat-10.1.15"

# Verificar arquivos de configuração
$configFiles = @(
    "$tomcatHome\conf\server.xml",
    "$tomcatHome\conf\context.xml",
    "$tomcatHome\conf\tomcat-users.xml",
    "$tomcatHome\conf\logging.properties",
    "$tomcatHome\bin\setenv.bat"
)

Write-Host "=== VERIFICANDO ARQUIVOS DE CONFIGURAÇÃO ===" -ForegroundColor Cyan
foreach ($file in $configFiles) {
    if (Test-Path $file) {
        Write-Host "✅ $file" -ForegroundColor Green
    } else {
        Write-Host "❌ $file - FALTANDO" -ForegroundColor Red
    }
}

# Testar sintaxe XML
Write-Host ""
Write-Host "=== VALIDANDO SINTAXE XML ===" -ForegroundColor Cyan

$xmlFiles = @(
    "$tomcatHome\conf\server.xml",
    "$tomcatHome\conf\context.xml",
    "$tomcatHome\conf\tomcat-users.xml"
)

foreach ($xmlFile in $xmlFiles) {
    if (Test-Path $xmlFile) {
        try {
            [xml]$xml = Get-Content $xmlFile
            Write-Host "✅ $(Split-Path $xmlFile -Leaf) - XML válido" -ForegroundColor Green
        } catch {
            Write-Host "❌ $(Split-Path $xmlFile -Leaf) - XML inválido: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# Verificar DataSources
Write-Host ""
Write-Host "=== VERIFICANDO DATASOURCES ===" -ForegroundColor Cyan

if (Test-Path "$tomcatHome\conf\context.xml") {
    $contextXml = Get-Content "$tomcatHome\conf\context.xml" -Raw
    
    $dataSources = @("WebpCrmDev", "VisionnaireDevMysql", "FiepeDevSqlserver", "CitsProdPostgresql")
    
    foreach ($ds in $dataSources) {
        if ($contextXml -match $ds) {
            Write-Host "✅ DataSource $ds configurado" -ForegroundColor Green
        } else {
            Write-Host "❌ DataSource $ds não encontrado" -ForegroundColor Red
        }
    }
}

Write-Host ""
Write-Host "=== TESTE CONCLUÍDO ===" -ForegroundColor Yellow
```

## Checklist de Validação

### **Configurações Básicas:**
- [ ] server.xml configurado e válido
- [ ] context.xml com DataSources configurados
- [ ] setenv.bat com otimizações Java 21
- [ ] logging.properties configurado
- [ ] tomcat-users.xml configurado

### **Performance:**
- [ ] Heap memory configurado (4GB)
- [ ] G1GC configurado
- [ ] Connection pools otimizados
- [ ] Compressão habilitada
- [ ] Cache de recursos configurado

### **Segurança:**
- [ ] Senhas hasheadas no tomcat-users.xml
- [ ] Roles e permissões configuradas
- [ ] Security constraints aplicadas
- [ ] Headers de segurança configurados

### **Monitoramento:**
- [ ] Logs estruturados configurados
- [ ] JMX habilitado
- [ ] Access logs configurados
- [ ] Rotação de logs configurada

### **Conectividade:**
- [ ] Todos os DataSources funcionando
- [ ] Conectividade com bancos testada
- [ ] Pool de conexões funcionando
- [ ] Timeouts configurados adequadamente

## Próximo Passo
**[4.3 Propriedades da Aplicação](./4.3-propriedades-aplicacao.md)**

---
**Status**: ⏳ Pendente
**Responsável**: DevOps/Administrador de Sistema
**Estimativa**: 1-2 dias
