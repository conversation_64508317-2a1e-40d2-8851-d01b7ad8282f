# 1.2 Análise de Compatibilidade de APIs

## Objetivo
Verificar uso de APIs depreciadas ou removidas entre Java 8 e Java 21 no código fonte da aplicação.

## Pré-requisitos
- Inventário de dependências concluído (1.1)
- Acesso ao código fonte em WEB-INF/src/
- Ferramentas de análise estática

## Tempo Estimado
6-8 horas

## Passos Detalhados

### 1. APIs Removidas no Java 21

#### Principais APIs Removidas/Depreciadas:
```java
// SecurityManager (removido no Java 21)
System.setSecurityManager()
System.getSecurityManager()

// Applet API (removido)
java.applet.*

// RMI Activation (removido)
java.rmi.activation.*

// CORBA (removido no Java 11)
javax.rmi.CORBA.*
org.omg.*

// Nashorn JavaScript Engine (removido no Java 15)
javax.script.ScriptEngineManager (para JavaScript)

// Thread.stop(), Thread.suspend(), Thread.resume() (depreciados)
Thread.stop()
Thread.suspend()
Thread.resume()
```

### 2. Script de Análise de Código

Criar `analyze_java_apis.ps1`:

```powershell
# Script para analisar uso de APIs problemáticas
param(
    [string]$SourcePath = "Source\WEB-INF\src",
    [string]$OutputFile = "API_COMPATIBILITY_REPORT.md"
)

$removedAPIs = @(
    "System\.setSecurityManager",
    "System\.getSecurityManager",
    "java\.applet\.",
    "javax\.rmi\.CORBA\.",
    "org\.omg\.",
    "Thread\.stop\(\)",
    "Thread\.suspend\(\)",
    "Thread\.resume\(\)",
    "sun\.",
    "com\.sun\."
)

$deprecatedAPIs = @(
    "new Date\(",
    "Calendar\.getInstance\(\)",
    "SimpleDateFormat",
    "Hashtable",
    "Vector",
    "StringBuffer" # em alguns contextos
)

"# Relatório de Compatibilidade de APIs" | Out-File $OutputFile
"" | Out-File $OutputFile -Append
"## Data da Análise: $(Get-Date)" | Out-File $OutputFile -Append
"" | Out-File $OutputFile -Append

"## APIs Removidas Encontradas" | Out-File $OutputFile -Append
"" | Out-File $OutputFile -Append

$foundIssues = $false

foreach ($api in $removedAPIs) {
    $matches = Get-ChildItem -Path $SourcePath -Recurse -Include "*.java" | 
               Select-String -Pattern $api
    
    if ($matches) {
        $foundIssues = $true
        "### $api" | Out-File $OutputFile -Append
        foreach ($match in $matches) {
            "- **Arquivo**: $($match.Filename)" | Out-File $OutputFile -Append
            "  - **Linha**: $($match.LineNumber)" | Out-File $OutputFile -Append
            "  - **Código**: ``$($match.Line.Trim())``" | Out-File $OutputFile -Append
        }
        "" | Out-File $OutputFile -Append
    }
}

if (-not $foundIssues) {
    "✅ Nenhuma API removida encontrada." | Out-File $OutputFile -Append
}

"## APIs Depreciadas Encontradas" | Out-File $OutputFile -Append
"" | Out-File $OutputFile -Append

$foundDeprecated = $false

foreach ($api in $deprecatedAPIs) {
    $matches = Get-ChildItem -Path $SourcePath -Recurse -Include "*.java" | 
               Select-String -Pattern $api
    
    if ($matches) {
        $foundDeprecated = $true
        "### $api" | Out-File $OutputFile -Append
        foreach ($match in $matches[0..4]) { # Limitar a 5 ocorrências
            "- **Arquivo**: $($match.Filename)" | Out-File $OutputFile -Append
            "  - **Linha**: $($match.LineNumber)" | Out-File $OutputFile -Append
        }
        if ($matches.Count -gt 5) {
            "- ... e mais $($matches.Count - 5) ocorrências" | Out-File $OutputFile -Append
        }
        "" | Out-File $OutputFile -Append
    }
}

if (-not $foundDeprecated) {
    "✅ Nenhuma API depreciada crítica encontrada." | Out-File $OutputFile -Append
}

Write-Host "Relatório salvo em: $OutputFile"
```

### 3. Análise Específica do Código WebPublication

#### Verificação Manual de Classes Principais:

```bash
# Buscar por imports problemáticos
grep -r "import sun\." Source/WEB-INF/src/ || echo "Nenhum import sun.* encontrado"
grep -r "import com\.sun\." Source/WEB-INF/src/ || echo "Nenhum import com.sun.* encontrado"

# Verificar uso de SecurityManager
grep -r "SecurityManager" Source/WEB-INF/src/ || echo "SecurityManager não encontrado"

# Verificar uso de APIs de data antigas
grep -r "new Date(" Source/WEB-INF/src/ | wc -l
grep -r "SimpleDateFormat" Source/WEB-INF/src/ | wc -l
```

### 4. Análise de Reflection e Module System

#### Verificar uso de Reflection:
```powershell
# Script para encontrar uso de reflection
$reflectionPatterns = @(
    "Class\.forName",
    "\.getDeclaredField",
    "\.getDeclaredMethod",
    "\.setAccessible",
    "\.newInstance"
)

"## Uso de Reflection" | Out-File API_COMPATIBILITY_REPORT.md -Append
"" | Out-File API_COMPATIBILITY_REPORT.md -Append

foreach ($pattern in $reflectionPatterns) {
    $matches = Get-ChildItem -Path "Source\WEB-INF\src" -Recurse -Include "*.java" | 
               Select-String -Pattern $pattern
    
    if ($matches) {
        "### $pattern" | Out-File API_COMPATIBILITY_REPORT.md -Append
        "Encontradas $($matches.Count) ocorrências" | Out-File API_COMPATIBILITY_REPORT.md -Append
        "" | Out-File API_COMPATIBILITY_REPORT.md -Append
    }
}
```

### 5. Verificação de Compatibilidade javax vs jakarta

```powershell
# Verificar imports javax que precisam migrar para jakarta
$javaxPatterns = @(
    "javax\.servlet\.",
    "javax\.persistence\.",
    "javax\.annotation\.",
    "javax\.ejb\.",
    "javax\.faces\.",
    "javax\.jms\.",
    "javax\.mail\.",
    "javax\.validation\.",
    "javax\.ws\.rs\.",
    "javax\.xml\.bind\.",
    "javax\.xml\.soap\.",
    "javax\.xml\.ws\."
)

"## Migração javax para jakarta" | Out-File API_COMPATIBILITY_REPORT.md -Append
"" | Out-File API_COMPATIBILITY_REPORT.md -Append

foreach ($pattern in $javaxPatterns) {
    $matches = Get-ChildItem -Path "Source\WEB-INF\src" -Recurse -Include "*.java" | 
               Select-String -Pattern $pattern
    
    if ($matches) {
        "### $pattern" | Out-File API_COMPATIBILITY_REPORT.md -Append
        "⚠️ **Requer migração para jakarta**: $($matches.Count) ocorrências" | Out-File API_COMPATIBILITY_REPORT.md -Append
        
        # Mostrar alguns exemplos
        foreach ($match in $matches[0..2]) {
            "- $($match.Filename):$($match.LineNumber)" | Out-File API_COMPATIBILITY_REPORT.md -Append
        }
        "" | Out-File API_COMPATIBILITY_REPORT.md -Append
    }
}
```

### 6. Análise de Dependências Internas

#### PSS Framework Analysis:
```java
// Verificar uso do framework PSS (Visionnaire)
// Localizado em: com.visionnaire.PSS.*

// Classes identificadas que usam PSS:
// - com.visionnaire.webpublication.business.HistoryImpl
// - com.visionnaire.webpublication.config.GeneralConfigImpl
// - com.visionnaire.webpublication.email.EmailOriginImpl

// Imports típicos encontrados:
import com.visionnaire.PSS.pid;
import com.visionnaire.PSS.client.NotDisconnectedException;
import com.visionnaire.PSS.client.BusinessObjectImpl;
import com.visionnaire.PSS.client.PList;
```

### 7. Relatório de Compatibilidade

Criar `JAVA21_COMPATIBILITY_REPORT.md`:

```markdown
# Relatório de Compatibilidade Java 21 - WebPublication

## Resumo Executivo
- **Total de arquivos Java analisados**: [NÚMERO]
- **APIs removidas encontradas**: [NÚMERO]
- **APIs depreciadas encontradas**: [NÚMERO]
- **Nível de risco**: [BAIXO/MÉDIO/ALTO]

## Principais Descobertas

### ✅ Pontos Positivos
- Não foram encontradas APIs críticas removidas
- Uso limitado de reflection
- Código relativamente moderno para Java 8

### ⚠️ Pontos de Atenção
- Uso extensivo de javax.servlet (migração para jakarta necessária)
- Dependência do framework PSS proprietário
- Algumas APIs de data antigas em uso

### ❌ Problemas Críticos
- [Listar problemas que impedem migração]

## Ações Necessárias

### Imediatas
1. Verificar compatibilidade do PSS Framework com Java 21
2. Planejar migração javax → jakarta
3. Atualizar dependências críticas

### Médio Prazo
1. Modernizar APIs de data para java.time
2. Revisar uso de reflection
3. Atualizar padrões de código

### Longo Prazo
1. Considerar migração para Spring Boot
2. Modernizar arquitetura geral
3. Implementar testes automatizados

## Estimativa de Esforço
- **Migração javax → jakarta**: 2-3 dias
- **Atualização de dependências**: 1-2 semanas
- **Testes e validação**: 1-2 semanas
- **Total estimado**: 3-5 semanas
```

### 8. Ferramentas de Análise Automatizada

#### Usando jdeps (Java Dependency Analysis):
```bash
# Analisar dependências internas
jdeps --class-path "WEB-INF\lib\*" WEB-INF\classes\

# Verificar uso de APIs internas
jdeps --jdk-internals --class-path "WEB-INF\lib\*" WEB-INF\classes\

# Analisar módulos necessários
jdeps --print-module-deps --class-path "WEB-INF\lib\*" WEB-INF\classes\
```

### 9. Checklist de Verificação

- [ ] APIs removidas verificadas
- [ ] APIs depreciadas catalogadas
- [ ] Uso de reflection analisado
- [ ] Migração javax → jakarta mapeada
- [ ] Framework PSS analisado
- [ ] Relatório de compatibilidade gerado
- [ ] Estimativas de esforço documentadas
- [ ] Plano de ação definido

## Próximo Passo
**[1.3 Análise de Drivers de Banco](./1.3-analise-drivers.md)**

---
**Status**: ⏳ Pendente
**Responsável**: Arquiteto de Software
**Estimativa**: 6-8 horas
