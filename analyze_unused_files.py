#!/usr/bin/env python3
"""
Script para identificar arquivos Java e JSP não utilizados no projeto
"""

import os
import re
import glob
from pathlib import Path
from collections import defaultdict

class UnusedFileAnalyzer:
    def __init__(self, project_root):
        self.project_root = Path(project_root)
        self.java_files = []
        self.jsp_files = []
        self.references = defaultdict(set)
        self.class_to_file = {}
        
    def find_files(self):
        """Encontra todos os arquivos Java e JSP"""
        print("Procurando arquivos Java e JSP...")
        
        # Encontrar arquivos Java
        for java_file in self.project_root.rglob("*.java"):
            self.java_files.append(java_file)
            
        # Encontrar arquivos JSP
        for jsp_file in self.project_root.rglob("*.jsp"):
            self.jsp_files.append(jsp_file)
            
        print(f"Encontrados {len(self.java_files)} arquivos Java")
        print(f"Encontrados {len(self.jsp_files)} arquivos JSP")
        
    def extract_class_names(self):
        """Extrai nomes de classes dos arquivos Java"""
        print("Extraindo nomes de classes...")
        
        for java_file in self.java_files:
            try:
                with open(java_file, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    
                # Procurar declarações de classe
                class_matches = re.findall(r'(?:public\s+)?(?:abstract\s+)?class\s+(\w+)', content)
                interface_matches = re.findall(r'(?:public\s+)?interface\s+(\w+)', content)
                
                for class_name in class_matches + interface_matches:
                    self.class_to_file[class_name] = java_file
                    
            except Exception as e:
                print(f"Erro ao processar {java_file}: {e}")
                
    def analyze_references(self):
        """Analisa referências entre arquivos"""
        print("Analisando referências...")
        
        all_files = self.java_files + self.jsp_files
        
        for file_path in all_files:
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    
                # Procurar imports Java
                import_matches = re.findall(r'import\s+(?:static\s+)?([a-zA-Z_][a-zA-Z0-9_.]*);', content)
                for import_name in import_matches:
                    class_name = import_name.split('.')[-1]
                    if class_name in self.class_to_file:
                        self.references[file_path].add(self.class_to_file[class_name])
                
                # Procurar referências diretas a classes
                for class_name in self.class_to_file.keys():
                    if re.search(r'\b' + re.escape(class_name) + r'\b', content):
                        if self.class_to_file[class_name] != file_path:  # Não contar auto-referência
                            self.references[file_path].add(self.class_to_file[class_name])
                
                # Para JSPs, procurar includes e forwards
                if file_path.suffix == '.jsp':
                    jsp_includes = re.findall(r'<%@\s*include\s+file\s*=\s*["\']([^"\']+)["\']', content)
                    jsp_forwards = re.findall(r'<jsp:forward\s+page\s*=\s*["\']([^"\']+)["\']', content)
                    
                    for jsp_ref in jsp_includes + jsp_forwards:
                        # Resolver caminho relativo
                        try:
                            ref_path = (file_path.parent / jsp_ref).resolve()
                            if ref_path.exists() and ref_path in self.jsp_files:
                                self.references[file_path].add(ref_path)
                        except:
                            pass
                            
            except Exception as e:
                print(f"Erro ao analisar referências em {file_path}: {e}")
    
    def find_unused_files(self):
        """Identifica arquivos não utilizados"""
        print("Identificando arquivos não utilizados...")
        
        all_files = set(self.java_files + self.jsp_files)
        referenced_files = set()
        
        # Coletar todos os arquivos referenciados
        for refs in self.references.values():
            referenced_files.update(refs)
        
        # Arquivos especiais que sempre devem ser considerados como usados
        special_files = set()
        
        # Procurar por arquivos de entrada (servlets, controllers, etc.)
        for java_file in self.java_files:
            try:
                with open(java_file, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    # Servlets, Controllers, Actions, etc.
                    if any(keyword in content for keyword in [
                        'extends HttpServlet', 'extends ActionSupport', 
                        '@Controller', '@RestController', '@WebServlet',
                        'implements Action', 'extends Action'
                    ]):
                        special_files.add(java_file)
            except:
                pass
        
        # JSPs especiais (index, error pages, etc.)
        for jsp_file in self.jsp_files:
            if any(name in jsp_file.name.lower() for name in [
                'index', 'error', 'login', 'main', 'home'
            ]):
                special_files.add(jsp_file)
        
        # Arquivos não utilizados = todos - referenciados - especiais
        unused_files = all_files - referenced_files - special_files
        
        return unused_files, special_files
    
    def generate_report(self):
        """Gera relatório de arquivos não utilizados"""
        unused_files, special_files = self.find_unused_files()
        
        print("\n" + "="*80)
        print("RELATÓRIO DE ARQUIVOS NÃO UTILIZADOS")
        print("="*80)
        
        # Separar por tipo
        unused_java = [f for f in unused_files if f.suffix == '.java']
        unused_jsp = [f for f in unused_files if f.suffix == '.jsp']
        
        print(f"\nResumo:")
        print(f"- Total de arquivos Java: {len(self.java_files)}")
        print(f"- Total de arquivos JSP: {len(self.jsp_files)}")
        print(f"- Arquivos Java não utilizados: {len(unused_java)}")
        print(f"- Arquivos JSP não utilizados: {len(unused_jsp)}")
        print(f"- Arquivos especiais (sempre considerados usados): {len(special_files)}")
        
        if unused_java:
            print(f"\n📁 ARQUIVOS JAVA NÃO UTILIZADOS ({len(unused_java)}):")
            print("-" * 50)
            for java_file in sorted(unused_java):
                rel_path = java_file.relative_to(self.project_root)
                print(f"  {rel_path}")
        
        if unused_jsp:
            print(f"\n📄 ARQUIVOS JSP NÃO UTILIZADOS ({len(unused_jsp)}):")
            print("-" * 50)
            for jsp_file in sorted(unused_jsp):
                rel_path = jsp_file.relative_to(self.project_root)
                print(f"  {rel_path}")
        
        # Salvar relatório em arquivo
        with open('unused_files_report.txt', 'w', encoding='utf-8') as f:
            f.write("RELATÓRIO DE ARQUIVOS NÃO UTILIZADOS\n")
            f.write("="*50 + "\n\n")
            
            f.write(f"Resumo:\n")
            f.write(f"- Total de arquivos Java: {len(self.java_files)}\n")
            f.write(f"- Total de arquivos JSP: {len(self.jsp_files)}\n")
            f.write(f"- Arquivos Java não utilizados: {len(unused_java)}\n")
            f.write(f"- Arquivos JSP não utilizados: {len(unused_jsp)}\n\n")
            
            if unused_java:
                f.write(f"ARQUIVOS JAVA NÃO UTILIZADOS:\n")
                f.write("-" * 30 + "\n")
                for java_file in sorted(unused_java):
                    rel_path = java_file.relative_to(self.project_root)
                    f.write(f"{rel_path}\n")
                f.write("\n")
            
            if unused_jsp:
                f.write(f"ARQUIVOS JSP NÃO UTILIZADOS:\n")
                f.write("-" * 30 + "\n")
                for jsp_file in sorted(unused_jsp):
                    rel_path = jsp_file.relative_to(self.project_root)
                    f.write(f"{rel_path}\n")
        
        print(f"\n✅ Relatório salvo em: unused_files_report.txt")
        
        return unused_files

def main():
    project_root = "."
    
    analyzer = UnusedFileAnalyzer(project_root)
    analyzer.find_files()
    analyzer.extract_class_names()
    analyzer.analyze_references()
    unused_files = analyzer.generate_report()
    
    print(f"\n🎯 Análise concluída! Encontrados {len(unused_files)} arquivos potencialmente não utilizados.")
    print("⚠️  IMPORTANTE: Revise manualmente antes de remover qualquer arquivo!")

if __name__ == "__main__":
    main()
