# 3.2 Atualização de Bibliotecas Core

## Objetivo
Atualizar Apache Commons, Jackson, Log4j, Elasticsearch e outras bibliotecas principais para versões compatíveis com Java 21.

## Pré-requisitos
- JDK 21 instalado e configurado
- Inventário de dependências concluído
- Ambiente de teste preparado

## Tempo Estimado
1-2 semanas

## Bibliotecas Core Identificadas

### Apache Commons Libraries
```
Atuais → Java 21 Compatíveis:
- commons-beanutils-1.8.0.jar → commons-beanutils-1.9.4.jar
- commons-collections-3.2.jar → commons-collections4-4.4.jar
- commons-dbcp.jar → commons-dbcp2-2.9.0.jar
- commons-email-1.3.1.jar → commons-email-1.5.jar
- commons-fileupload-1.2.1.jar → commons-fileupload-1.5.jar
- commons-io-2.6.jar → commons-io-2.11.0.jar
- commons-lang-2.6.jar → commons-lang3-3.12.0.jar
- commons-logging-api-1.1.jar → commons-logging-1.2.jar
- commons-pool.jar → commons-pool2-2.11.1.jar
```

### JSON Processing
```
Atuais → Java 21 Compatíveis:
- jackson-core-2.8.1.jar → jackson-core-2.15.2.jar
- jackson-databind-2.6.2.jar → jackson-databind-2.15.2.jar
- jackson-annotations-2.6.0.jar → jackson-annotations-2.15.2.jar
- gson-2.2.2.jar → gson-2.10.1.jar
```

### Logging
```
Atuais → Java 21 Compatíveis:
- log4j-*.jar → log4j-core-2.20.0.jar + log4j-api-2.20.0.jar
- apache-log4j-extras-1.2.17.jar → (remover, funcionalidade incluída no Log4j 2)
```

### Search e Analytics
```
Atuais → Java 21 Compatíveis:
- elasticsearch-2.4.4.jar → elasticsearch-8.9.0.jar (major upgrade)
```

## Passos Detalhados

### 1. Backup das Bibliotecas Atuais

```bash
# Criar backup das bibliotecas atuais
mkdir "backup\lib-java8"
copy "Source\WEB-INF\lib\*" "backup\lib-java8\"

# Documentar versões atuais
dir "Source\WEB-INF\lib\*.jar" > "backup\lib-java8\original_versions.txt"
```

### 2. Download das Novas Versões

#### Script PowerShell para Download:
```powershell
# download_libraries.ps1
$libraries = @{
    "commons-beanutils" = "https://repo1.maven.org/maven2/commons-beanutils/commons-beanutils/1.9.4/commons-beanutils-1.9.4.jar"
    "commons-collections4" = "https://repo1.maven.org/maven2/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.jar"
    "commons-dbcp2" = "https://repo1.maven.org/maven2/org/apache/commons/commons-dbcp2/2.9.0/commons-dbcp2-2.9.0.jar"
    "commons-email" = "https://repo1.maven.org/maven2/org/apache/commons/commons-email/1.5/commons-email-1.5.jar"
    "commons-fileupload" = "https://repo1.maven.org/maven2/commons-fileupload/commons-fileupload/1.5/commons-fileupload-1.5.jar"
    "commons-io" = "https://repo1.maven.org/maven2/commons-io/commons-io/2.11.0/commons-io-2.11.0.jar"
    "commons-lang3" = "https://repo1.maven.org/maven2/org/apache/commons/commons-lang3/3.12.0/commons-lang3-3.12.0.jar"
    "commons-logging" = "https://repo1.maven.org/maven2/commons-logging/commons-logging/1.2/commons-logging-1.2.jar"
    "commons-pool2" = "https://repo1.maven.org/maven2/org/apache/commons/commons-pool2/2.11.1/commons-pool2-2.11.1.jar"
    "jackson-core" = "https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-core/2.15.2/jackson-core-2.15.2.jar"
    "jackson-databind" = "https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-databind/2.15.2/jackson-databind-2.15.2.jar"
    "jackson-annotations" = "https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-annotations/2.15.2/jackson-annotations-2.15.2.jar"
    "gson" = "https://repo1.maven.org/maven2/com/google/code/gson/gson/2.10.1/gson-2.10.1.jar"
    "log4j-core" = "https://repo1.maven.org/maven2/org/apache/logging/log4j/log4j-core/2.20.0/log4j-core-2.20.0.jar"
    "log4j-api" = "https://repo1.maven.org/maven2/org/apache/logging/log4j/log4j-api/2.20.0/log4j-api-2.20.0.jar"
}

$downloadPath = "temp\new-libraries"
New-Item -ItemType Directory -Force -Path $downloadPath

foreach ($lib in $libraries.GetEnumerator()) {
    $fileName = Split-Path $lib.Value -Leaf
    $filePath = Join-Path $downloadPath $fileName
    
    Write-Host "Downloading $($lib.Key)..."
    try {
        Invoke-WebRequest -Uri $lib.Value -OutFile $filePath
        Write-Host "✅ Downloaded: $fileName"
    } catch {
        Write-Host "❌ Failed to download: $($lib.Key)"
        Write-Host "Error: $($_.Exception.Message)"
    }
}

Write-Host "Download completed. Files saved to: $downloadPath"
```

### 3. Atualização Gradual das Bibliotecas

#### Fase 3.2.1: Apache Commons (Baixo Risco)
```bash
# Remover versões antigas
del "Source\WEB-INF\lib\commons-io-2.6.jar"
del "Source\WEB-INF\lib\commons-logging-api-1.1.jar"

# Adicionar versões novas
copy "temp\new-libraries\commons-io-2.11.0.jar" "Source\WEB-INF\lib\"
copy "temp\new-libraries\commons-logging-1.2.jar" "Source\WEB-INF\lib\"

# Testar compilação
ant compile
```

#### Fase 3.2.2: Jackson (Médio Risco)
```bash
# Backup específico do Jackson
mkdir "backup\jackson-old"
copy "Source\WEB-INF\lib\jackson-*.jar" "backup\jackson-old\"

# Remover versões antigas
del "Source\WEB-INF\lib\jackson-core-2.8.1.jar"
del "Source\WEB-INF\lib\jackson-databind-2.6.2.jar"
del "Source\WEB-INF\lib\jackson-annotations-2.6.0.jar"

# Adicionar versões novas
copy "temp\new-libraries\jackson-core-2.15.2.jar" "Source\WEB-INF\lib\"
copy "temp\new-libraries\jackson-databind-2.15.2.jar" "Source\WEB-INF\lib\"
copy "temp\new-libraries\jackson-annotations-2.15.2.jar" "Source\WEB-INF\lib\"

# Testar compilação
ant compile
```

#### Fase 3.2.3: Log4j (Alto Risco)
```bash
# Backup específico do Log4j
mkdir "backup\log4j-old"
copy "Source\WEB-INF\lib\log4j*.jar" "backup\log4j-old\"
copy "Source\WEB-INF\lib\apache-log4j-extras*.jar" "backup\log4j-old\"

# Remover versões antigas
del "Source\WEB-INF\lib\log4j*.jar"
del "Source\WEB-INF\lib\apache-log4j-extras*.jar"

# Adicionar Log4j 2
copy "temp\new-libraries\log4j-core-2.20.0.jar" "Source\WEB-INF\lib\"
copy "temp\new-libraries\log4j-api-2.20.0.jar" "Source\WEB-INF\lib\"

# Adicionar bridge para compatibilidade
# log4j-1.2-api-2.20.0.jar (para compatibilidade com código Log4j 1.x)
```

### 4. Mudanças de Código Necessárias

#### Commons Lang 2 → 3:
```java
// Antes (Commons Lang 2)
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.ArrayUtils;

// Depois (Commons Lang 3)
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.ArrayUtils;
```

#### Commons Collections 3 → 4:
```java
// Antes
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

// Depois
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
```

#### Log4j 1 → 2:
```java
// Antes (Log4j 1)
import org.apache.log4j.Logger;
private static final Logger logger = Logger.getLogger(MyClass.class);

// Depois (Log4j 2)
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
private static final Logger logger = LogManager.getLogger(MyClass.class);
```

### 5. Configuração do Log4j 2

#### Criar log4j2.xml:
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN">
    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n"/>
        </Console>
        
        <File name="FileAppender" fileName="logs/webpublication.log">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n"/>
        </File>
        
        <RollingFile name="RollingFileAppender" fileName="logs/webpublication.log"
                     filePattern="logs/webpublication-%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy />
                <SizeBasedTriggeringPolicy size="10 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>
    </Appenders>
    
    <Loggers>
        <Logger name="com.visionnaire.webpublication" level="DEBUG" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="RollingFileAppender"/>
        </Logger>
        
        <Logger name="org.apache" level="WARN" additivity="false">
            <AppenderRef ref="Console"/>
        </Logger>
        
        <Root level="INFO">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="FileAppender"/>
        </Root>
    </Loggers>
</Configuration>
```

### 6. Script de Validação

#### validate_libraries.ps1:
```powershell
# Script para validar bibliotecas atualizadas
$libPath = "Source\WEB-INF\lib"
$requiredLibs = @(
    "commons-io-2.11.0.jar",
    "commons-lang3-3.12.0.jar",
    "jackson-core-2.15.2.jar",
    "jackson-databind-2.15.2.jar",
    "log4j-core-2.20.0.jar",
    "log4j-api-2.20.0.jar"
)

Write-Host "Validando bibliotecas atualizadas..."
Write-Host "=================================="

$allPresent = $true

foreach ($lib in $requiredLibs) {
    $libFile = Join-Path $libPath $lib
    if (Test-Path $libFile) {
        Write-Host "✅ $lib - OK"
    } else {
        Write-Host "❌ $lib - MISSING"
        $allPresent = $false
    }
}

Write-Host ""
if ($allPresent) {
    Write-Host "✅ Todas as bibliotecas necessárias estão presentes"
} else {
    Write-Host "❌ Algumas bibliotecas estão faltando"
}

# Verificar duplicatas
Write-Host ""
Write-Host "Verificando duplicatas..."
$jars = Get-ChildItem "$libPath\*.jar" | ForEach-Object { $_.Name }
$duplicates = $jars | Group-Object { ($_ -split '-')[0] } | Where-Object { $_.Count -gt 1 }

if ($duplicates) {
    Write-Host "⚠️ Possíveis duplicatas encontradas:"
    foreach ($dup in $duplicates) {
        Write-Host "  - $($dup.Name): $($dup.Group -join ', ')"
    }
} else {
    Write-Host "✅ Nenhuma duplicata encontrada"
}
```

### 7. Testes de Compilação

```bash
# Teste de compilação completa
cd Source
ant clean
ant compile

# Verificar erros específicos
ant compile 2>&1 | findstr "error"
ant compile 2>&1 | findstr "warning"
```

### 8. Rollback Plan

#### Script de Rollback:
```batch
@echo off
echo Executando rollback das bibliotecas...

echo Removendo bibliotecas novas...
del "Source\WEB-INF\lib\commons-io-2.11.0.jar"
del "Source\WEB-INF\lib\commons-lang3-3.12.0.jar"
del "Source\WEB-INF\lib\jackson-core-2.15.2.jar"
del "Source\WEB-INF\lib\jackson-databind-2.15.2.jar"
del "Source\WEB-INF\lib\jackson-annotations-2.15.2.jar"
del "Source\WEB-INF\lib\log4j-core-2.20.0.jar"
del "Source\WEB-INF\lib\log4j-api-2.20.0.jar"

echo Restaurando bibliotecas originais...
copy "backup\lib-java8\*" "Source\WEB-INF\lib\"

echo Rollback concluído.
pause
```

### 9. Checklist de Validação

- [ ] Backup das bibliotecas originais criado
- [ ] Novas bibliotecas baixadas e verificadas
- [ ] Atualização gradual executada
- [ ] Código atualizado para novas APIs
- [ ] Log4j 2 configurado
- [ ] Compilação bem-sucedida
- [ ] Testes básicos executados
- [ ] Script de rollback testado
- [ ] Documentação atualizada

## Próximo Passo
**[3.3 Frameworks Web](./3.3-frameworks-web.md)**

---
**Status**: ⏳ Pendente
**Responsável**: Desenvolvedor Senior
**Estimativa**: 1-2 semanas
