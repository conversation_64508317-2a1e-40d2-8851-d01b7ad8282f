# 1.1 Inventário de Dependências

## Objetivo
Catalogar todas as bibliotecas JAR em WEB-INF/lib e suas versões atuais para análise de compatibilidade com Java 21.

## Pré-requisitos
- Backup realizado (1.4)
- Acesso ao diretório Source/WEB-INF/lib/
- Ferramentas de análise de JAR

## Tempo Estimado
4-6 horas

## Passos Detalhados

### 1. Listagem Inicial de JARs

```bash
# Navegar para o diretório de bibliotecas
cd "Source\WEB-INF\lib"

# Listar todos os JARs com tamanhos
dir *.jar /o:n > jar_list.txt

# Contar total de JARs
dir *.jar | find /c ".jar"
```

### 2. Análise Detalhada de Versões

Criar script PowerShell `analyze_jars.ps1`:

```powershell
# Script para analisar JARs e extrair versões
$jarPath = "Source\WEB-INF\lib"
$outputFile = "DEPENDENCIES_INVENTORY.md"

"# Inventário de Dependências - WebPublication" | Out-File $outputFile
"" | Out-File $outputFile -Append
"## Data da Análise: $(Get-Date)" | Out-File $outputFile -Append
"" | Out-File $outputFile -Append
"| JAR File | Versão Detectada | Tamanho | Categoria | Status Java 21 |" | Out-File $outputFile -Append
"|----------|------------------|---------|-----------|-----------------|" | Out-File $outputFile -Append

Get-ChildItem "$jarPath\*.jar" | ForEach-Object {
    $jarName = $_.Name
    $jarSize = [math]::Round($_.Length / 1MB, 2)
    
    # Tentar extrair versão do nome do arquivo
    $version = "Unknown"
    if ($jarName -match "(\d+\.[\d\.]+)") {
        $version = $matches[1]
    }
    
    # Categorizar biblioteca
    $category = "Other"
    switch -Regex ($jarName) {
        "^commons-" { $category = "Apache Commons" }
        "^jackson-" { $category = "Jackson JSON" }
        "^log4j" { $category = "Logging" }
        "^mysql|^oracle|^postgresql|^mssql" { $category = "Database Driver" }
        "^servlet|^jsp" { $category = "Java EE" }
        "^spring" { $category = "Spring Framework" }
        "^hibernate" { $category = "ORM" }
        "^elasticsearch" { $category = "Search" }
        "^tiles|^struts" { $category = "Web Framework" }
        default { $category = "Third Party" }
    }
    
    "| $jarName | $version | ${jarSize}MB | $category | ⏳ Pending |" | Out-File $outputFile -Append
}

Write-Host "Inventário salvo em: $outputFile"
```

### 3. Dependências Identificadas (Baseado na Análise)

#### Drivers de Banco de Dados
```
- mysql-connector-java-*.jar
- ojdbc*.jar (Oracle)
- sqljdbc*.jar (SQL Server)  
- postgresql-*.jar
```

#### Frameworks Web
```
- servlet-api.jar
- jsp-api.jar
- tiles-*.jar
- dwr.jar
- displaytag-*.jar
```

#### Apache Commons
```
- commons-beanutils-1.8.0.jar
- commons-collections-3.2.jar
- commons-dbcp.jar
- commons-email-1.3.1.jar
- commons-fileupload-1.2.1.jar
- commons-io-2.6.jar
- commons-lang-2.6.jar
- commons-logging-api-1.1.jar
- commons-pool.jar
```

#### JSON/XML Processing
```
- jackson-core-2.8.1.jar
- jackson-databind-2.6.2.jar
- jackson-annotations-2.6.0.jar
- gson-2.2.2.jar
- dom4j-1.6.1.jar
```

#### Logging
```
- log4j-*.jar
- apache-log4j-extras-1.2.17.jar
```

#### Search e Analytics
```
- elasticsearch-2.4.4.jar
- lucene-*.jar
```

#### PDF e Imagem
```
- itext-1.3.1.jar
- itextpdf-5.3.5.jar
- jai_core-1.1.3.jar
- jai_codec-1.1.3.jar
- jai_imageio-1.1.jar
```

#### Utilitários
```
- guava-18.0.jar
- boon-0.33.jar
- compress-lzf-1.0.2.jar
```

### 4. Análise de Compatibilidade Inicial

Criar matriz de compatibilidade `COMPATIBILITY_MATRIX.md`:

```markdown
# Matriz de Compatibilidade Java 21

## Status Legend
- ✅ Compatível
- ⚠️ Requer atualização
- ❌ Incompatível
- ⏳ Análise pendente

## Dependências Críticas

### Drivers de Banco
| Biblioteca | Versão Atual | Status | Versão Java 21 | Notas |
|------------|--------------|--------|-----------------|-------|
| MySQL Connector | 5.x | ⚠️ | 8.0.33+ | Atualização obrigatória |
| Oracle JDBC | 11.x | ⚠️ | 21.x | Verificar versão específica |
| SQL Server JDBC | 6.x | ⚠️ | 12.x | Atualização recomendada |
| PostgreSQL | 9.x | ⚠️ | 42.x | Atualização obrigatória |

### Frameworks Web
| Biblioteca | Versão Atual | Status | Versão Java 21 | Notas |
|------------|--------------|--------|-----------------|-------|
| Servlet API | 2.4 | ❌ | 6.0+ | Migração para Jakarta EE |
| JSP API | 2.0 | ❌ | 4.0+ | Migração para Jakarta EE |
| Tiles | 2.x | ⚠️ | 3.0.8+ | Atualização necessária |
| DWR | 2.x | ⚠️ | 3.0+ | Verificar compatibilidade |

### Apache Commons
| Biblioteca | Versão Atual | Status | Versão Java 21 | Notas |
|------------|--------------|--------|-----------------|-------|
| Commons Lang | 2.6 | ⚠️ | 3.12+ | Mudanças de API |
| Commons IO | 2.6 | ⚠️ | 2.11+ | Compatível com ajustes |
| Commons Collections | 3.2 | ⚠️ | 4.4+ | Mudanças significativas |
| Commons Email | 1.3.1 | ⚠️ | 1.5+ | Atualização recomendada |

### JSON Processing
| Biblioteca | Versão Atual | Status | Versão Java 21 | Notas |
|------------|--------------|--------|-----------------|-------|
| Jackson Core | 2.8.1 | ⚠️ | 2.15+ | Atualização obrigatória |
| Jackson Databind | 2.6.2 | ❌ | 2.15+ | Vulnerabilidades conhecidas |
| Gson | 2.2.2 | ⚠️ | 2.10+ | Atualização recomendada |

### Logging
| Biblioteca | Versão Atual | Status | Versão Java 21 | Notas |
|------------|--------------|--------|-----------------|-------|
| Log4j | 1.x | ❌ | 2.20+ | Migração obrigatória |
| SLF4J | N/A | ⏳ | 2.0+ | Adicionar se necessário |
```

### 5. Script de Verificação de Dependências

Criar `check_dependencies.bat`:

```batch
@echo off
echo Verificando dependências para Java 21...
echo.

echo === DRIVERS DE BANCO ===
if exist "WEB-INF\lib\mysql-connector-java*.jar" (
    echo [FOUND] MySQL Connector
) else (
    echo [MISSING] MySQL Connector
)

if exist "WEB-INF\lib\ojdbc*.jar" (
    echo [FOUND] Oracle JDBC
) else (
    echo [MISSING] Oracle JDBC
)

echo.
echo === FRAMEWORKS WEB ===
if exist "WEB-INF\lib\servlet-api.jar" (
    echo [FOUND] Servlet API
) else (
    echo [MISSING] Servlet API
)

echo.
echo === LOGGING ===
if exist "WEB-INF\lib\log4j*.jar" (
    echo [FOUND] Log4j
) else (
    echo [MISSING] Log4j
)

echo.
echo === JSON PROCESSING ===
if exist "WEB-INF\lib\jackson*.jar" (
    echo [FOUND] Jackson
) else (
    echo [MISSING] Jackson
)

echo.
echo Verificação concluída. Consulte COMPATIBILITY_MATRIX.md para detalhes.
pause
```

### 6. Análise de Dependências Proprietárias

#### PSS Framework (Visionnaire)
```
- pss.jar
- acss-local.jar
- viter.jar
- vjdbc.jar
- vlog.jar
- vtask.jar
- vutil.jar
```

**Ação Necessária**: Contatar equipe Visionnaire para:
- Verificar compatibilidade com Java 21
- Obter versões atualizadas se necessário
- Documentar APIs internas utilizadas

### 7. Relatório de Dependências Problemáticas

Criar `PROBLEMATIC_DEPENDENCIES.md`:

```markdown
# Dependências Problemáticas - Java 21

## Críticas (Bloqueiam migração)
1. **Servlet API 2.4** - Incompatível, migração para Jakarta EE necessária
2. **Log4j 1.x** - Vulnerabilidades de segurança, migração obrigatória
3. **Jackson 2.6.x** - Vulnerabilidades conhecidas, atualização crítica

## Alto Risco
1. **PSS Framework** - Proprietário, compatibilidade desconhecida
2. **Elasticsearch 2.4.4** - Versão muito antiga
3. **Commons Collections 3.2** - Mudanças significativas de API

## Médio Risco
1. **Tiles 2.x** - Requer atualização
2. **DWR** - Verificar suporte ativo
3. **iText 1.3.1** - Versão muito antiga

## Baixo Risco
1. **Guava 18.0** - Compatível com atualização
2. **Commons IO 2.6** - Compatível
3. **DOM4J 1.6.1** - Estável
```

### 8. Próximos Passos

1. **Priorização**: Focar nas dependências críticas primeiro
2. **Pesquisa**: Investigar versões compatíveis com Java 21
3. **Testes**: Criar ambiente isolado para testes
4. **Documentação**: Manter registro de todas as mudanças

## Deliverables

- [ ] `DEPENDENCIES_INVENTORY.md` - Lista completa de JARs
- [ ] `COMPATIBILITY_MATRIX.md` - Matriz de compatibilidade
- [ ] `PROBLEMATIC_DEPENDENCIES.md` - Dependências problemáticas
- [ ] `check_dependencies.bat` - Script de verificação
- [ ] `analyze_jars.ps1` - Script de análise

## Próximo Passo
**[1.2 Análise de Compatibilidade de APIs](./1.2-analise-compatibilidade.md)**

---
**Status**: ⏳ Pendente
**Responsável**: Desenvolvedor Senior
**Estimativa**: 4-6 horas
