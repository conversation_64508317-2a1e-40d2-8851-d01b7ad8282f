# 2.2 Atualização do Apache Tomcat

## Objetivo
Migrar do Apache Tomcat 8.0.53 para versão compatível com Java 21 e Jakarta EE.

## Pré-requisitos
- JDK 21 instalado e configurado
- Backup do Tomcat 8.0.53 atual
- Permissões administrativas

## Tempo Estimado
4-6 horas

## Análise da Situação Atual

### **Tomcat Atual Identificado:**
- **Versão**: Apache Tomcat 8.0.53
- **Localização**: `D:\java\apache-tomcat-8.0.53`
- **Java Suportado**: Java 8
- **Servlet API**: 3.1
- **JSP API**: 2.3

### **Problemas com Java 21:**
- Tomcat 8.x não suporta Java 21
- APIs javax.* em vez de jakarta.*
- Configurações desatualizadas
- Possíveis problemas de segurança

## Escolha da Versão Target

### **Opções Disponíveis:**

#### **Tomcat 10.1.x (RECOMENDADO)**
- **Java**: 11, 17, 21
- **Servlet API**: 5.0 (Jakarta EE 9)
- **JSP API**: 3.0
- **Namespace**: jakarta.*
- **Status**: LTS, estável

#### **Tomcat 11.0.x**
- **Java**: 17, 21
- **Servlet API**: 6.0 (Jakarta EE 10)
- **JSP API**: 3.1
- **Namespace**: jakarta.*
- **Status**: Mais recente, menos testado

**Escolha: Tomcat 10.1.15** (versão LTS estável)

## Passos Detalhados

### 1. Backup do Tomcat Atual

```bash
# Parar Tomcat atual
net stop Tomcat8
# ou
D:\java\apache-tomcat-8.0.53\bin\shutdown.bat

# Criar backup completo
mkdir "D:\backup\tomcat-8.0.53-backup"
xcopy "D:\java\apache-tomcat-8.0.53" "D:\backup\tomcat-8.0.53-backup" /E /I /H /Y

# Backup específico de configurações
mkdir "backup\tomcat-configs"
copy "D:\java\apache-tomcat-8.0.53\conf\*" "backup\tomcat-configs\"
copy "D:\java\apache-tomcat-8.0.53\webapps\manager\*" "backup\tomcat-configs\manager\" /S
```

### 2. Download do Tomcat 10.1.15

```bash
# URL de download
# https://tomcat.apache.org/download-10.cgi
# Arquivo: apache-tomcat-10.1.15.zip

# Download via PowerShell
$url = "https://archive.apache.org/dist/tomcat/tomcat-10/v10.1.15/bin/apache-tomcat-10.1.15.zip"
$output = "apache-tomcat-10.1.15.zip"
Invoke-WebRequest -Uri $url -OutFile $output

# Ou download manual e extrair para:
# D:\java\apache-tomcat-10.1.15
```

### 3. Instalação do Tomcat 10.1.15

```bash
# Extrair para diretório padrão
# Extrair apache-tomcat-10.1.15.zip para D:\java\

# Verificar estrutura
dir "D:\java\apache-tomcat-10.1.15"
# Deve conter: bin, conf, lib, logs, temp, webapps, work
```

### 4. Configuração de Variáveis de Ambiente

#### Backup das Configurações Atuais:
```bash
echo %CATALINA_HOME% > catalina_home_backup.txt
echo %CATALINA_BASE% > catalina_base_backup.txt
```

#### Configurar Novas Variáveis:
```bash
# Via Command Prompt (como Administrador)
setx CATALINA_HOME "D:\java\apache-tomcat-10.1.15" /M
setx CATALINA_BASE "D:\java\apache-tomcat-10.1.15" /M

# Via PowerShell (como Administrador)
[Environment]::SetEnvironmentVariable("CATALINA_HOME", "D:\java\apache-tomcat-10.1.15", "Machine")
[Environment]::SetEnvironmentVariable("CATALINA_BASE", "D:\java\apache-tomcat-10.1.15", "Machine")
```

### 5. Migração de Configurações

#### 5.1 Migração do server.xml

**Backup e análise do atual:**
```xml
<!-- Tomcat 8.0.53 server.xml atual -->
<!-- Localização: D:\java\apache-tomcat-8.0.53\conf\server.xml -->
```

**Novo server.xml para Tomcat 10.1.15:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Server port="8005" shutdown="SHUTDOWN">
  <Listener className="org.apache.catalina.startup.VersionLoggerListener" />
  <Listener className="org.apache.catalina.core.AprLifecycleListener" SSLEngine="on" />
  <Listener className="org.apache.catalina.core.JreMemoryLeakPreventionListener" />
  <Listener className="org.apache.catalina.mbeans.GlobalResourcesLifecycleListener" />
  <Listener className="org.apache.catalina.core.ThreadLocalLeakPreventionListener" />

  <GlobalNamingResources>
    <Resource name="UserDatabase" auth="Container"
              type="org.apache.catalina.UserDatabase"
              description="User database that can be updated and saved"
              factory="org.apache.catalina.users.MemoryUserDatabaseFactory"
              pathname="conf/tomcat-users.xml" />
  </GlobalNamingResources>

  <Service name="Catalina">
    <Connector port="8080" protocol="HTTP/1.1"
               connectionTimeout="20000"
               redirectPort="8443"
               maxParameterCount="1000" />

    <!-- HTTPS Connector (se necessário) -->
    <!--
    <Connector port="8443" protocol="org.apache.coyote.http11.Http11NioProtocol"
               maxThreads="150" SSLEnabled="true">
        <UpgradeProtocol className="org.apache.coyote.http2.Http2Protocol" />
        <SSLHostConfig>
            <Certificate certificateKeystoreFile="conf/localhost-rsa.jks"
                         type="RSA" />
        </SSLHostConfig>
    </Connector>
    -->

    <Engine name="Catalina" defaultHost="localhost">
      <Realm className="org.apache.catalina.realm.LockOutRealm">
        <Realm className="org.apache.catalina.realm.UserDatabaseRealm"
               resourceName="UserDatabase"/>
      </Realm>

      <Host name="localhost" appBase="webapps"
            unpackWARs="true" autoDeploy="true">
        <Valve className="org.apache.catalina.valves.AccessLogValve" directory="logs"
               prefix="localhost_access_log" suffix=".txt"
               pattern="%h %l %u %t &quot;%r&quot; %s %b" />
      </Host>
    </Engine>
  </Service>
</Server>
```

#### 5.2 Migração do context.xml

**Analisar context.xml atual:**
```xml
<!-- Verificar configurações de DataSource em:
     - D:\java\apache-tomcat-8.0.53\conf\context.xml
     - Source\META-INF\context.xml
-->
```

**Novo context.xml (exemplo com DataSources):**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Context>
    <!-- Prevent memory leaks -->
    <Manager pathname="" />
    
    <!-- DataSource para Oracle CRM -->
    <Resource name="jdbc/WebpCrmDev"
              auth="Container"
              type="javax.sql.DataSource"
              driverClassName="oracle.jdbc.OracleDriver"
              url="**********************************************"
              username="WEBP_CRM_DEV"
              password="mSKqzKHzdDJk"
              maxTotal="20"
              maxIdle="10"
              maxWaitMillis="10000"
              testOnBorrow="true"
              validationQuery="SELECT 1 FROM DUAL" />

    <!-- DataSource para MySQL Visionnaire -->
    <Resource name="jdbc/VisionnaireDevMysql"
              auth="Container"
              type="javax.sql.DataSource"
              driverClassName="com.mysql.cj.jdbc.Driver"
              url="************************************************************************************"
              username="root"
              password="a4DqYcBvKB"
              maxTotal="20"
              maxIdle="10"
              maxWaitMillis="10000"
              testOnBorrow="true"
              validationQuery="SELECT 1" />

    <!-- DataSource para SQL Server FIEPE -->
    <Resource name="jdbc/FiepeDevSqlserver"
              auth="Container"
              type="javax.sql.DataSource"
              driverClassName="com.microsoft.sqlserver.jdbc.SQLServerDriver"
              url="***********************************************************"
              username="sa"
              password="a4DqYcBvKB"
              maxTotal="20"
              maxIdle="10"
              maxWaitMillis="10000"
              testOnBorrow="true"
              validationQuery="SELECT 1" />

    <!-- DataSource para PostgreSQL CITS -->
    <Resource name="jdbc/CitsProdPostgresql"
              auth="Container"
              type="javax.sql.DataSource"
              driverClassName="org.postgresql.Driver"
              url="***********************************************"
              username="postgres"
              password="a4DqYcBvKB"
              maxTotal="20"
              maxIdle="10"
              maxWaitMillis="10000"
              testOnBorrow="true"
              validationQuery="SELECT 1" />
</Context>
```

#### 5.3 Migração do tomcat-users.xml

```xml
<?xml version="1.0" encoding="UTF-8"?>
<tomcat-users xmlns="http://tomcat.apache.org/xml"
              xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
              xsi:schemaLocation="http://tomcat.apache.org/xml tomcat-users.xsd"
              version="1.0">
  
  <!-- Usuário para manager app -->
  <role rolename="manager-gui"/>
  <role rolename="manager-script"/>
  <role rolename="admin-gui"/>
  
  <user username="admin" password="admin123" roles="manager-gui,admin-gui"/>
  <user username="deployer" password="deploy123" roles="manager-script"/>
</tomcat-users>
```

### 6. Configuração de Logs

#### 6.1 Configurar logging.properties

```properties
# Tomcat 10.1.15 logging.properties
handlers = 1catalina.org.apache.juli.AsyncFileHandler, 2localhost.org.apache.juli.AsyncFileHandler, 3manager.org.apache.juli.AsyncFileHandler, java.util.logging.ConsoleHandler

.handlers = 1catalina.org.apache.juli.AsyncFileHandler, java.util.logging.ConsoleHandler

# Catalina log
1catalina.org.apache.juli.AsyncFileHandler.level = FINE
1catalina.org.apache.juli.AsyncFileHandler.directory = ${catalina.base}/logs
1catalina.org.apache.juli.AsyncFileHandler.prefix = catalina.
1catalina.org.apache.juli.AsyncFileHandler.maxDays = 90
1catalina.org.apache.juli.AsyncFileHandler.encoding = UTF-8

# Localhost log
2localhost.org.apache.juli.AsyncFileHandler.level = FINE
2localhost.org.apache.juli.AsyncFileHandler.directory = ${catalina.base}/logs
2localhost.org.apache.juli.AsyncFileHandler.prefix = localhost.
2localhost.org.apache.juli.AsyncFileHandler.maxDays = 90
2localhost.org.apache.juli.AsyncFileHandler.encoding = UTF-8

# Manager log
3manager.org.apache.juli.AsyncFileHandler.level = FINE
3manager.org.apache.juli.AsyncFileHandler.directory = ${catalina.base}/logs
3manager.org.apache.juli.AsyncFileHandler.prefix = manager.
3manager.org.apache.juli.AsyncFileHandler.maxDays = 90
3manager.org.apache.juli.AsyncFileHandler.encoding = UTF-8

# Console
java.util.logging.ConsoleHandler.level = FINE
java.util.logging.ConsoleHandler.formatter = org.apache.juli.OneLineFormatter
java.util.logging.ConsoleHandler.encoding = UTF-8

# WebPublication specific logging
com.visionnaire.webpublication.level = FINE
com.visionnaire.PSS.level = INFO
```

### 7. Configuração de Memória e Performance

#### 7.1 Criar setenv.bat

```batch
@echo off
rem Configurações de JVM para Tomcat 10.1.15 + Java 21

rem Configurações de memória
set CATALINA_OPTS=%CATALINA_OPTS% -Xms1024m
set CATALINA_OPTS=%CATALINA_OPTS% -Xmx2048m
set CATALINA_OPTS=%CATALINA_OPTS% -XX:MetaspaceSize=256m
set CATALINA_OPTS=%CATALINA_OPTS% -XX:MaxMetaspaceSize=512m

rem Garbage Collector (G1 recomendado para Java 21)
set CATALINA_OPTS=%CATALINA_OPTS% -XX:+UseG1GC
set CATALINA_OPTS=%CATALINA_OPTS% -XX:MaxGCPauseMillis=200
set CATALINA_OPTS=%CATALINA_OPTS% -XX:+UseStringDeduplication

rem Configurações de segurança e compatibilidade
set CATALINA_OPTS=%CATALINA_OPTS% --add-opens java.base/java.lang=ALL-UNNAMED
set CATALINA_OPTS=%CATALINA_OPTS% --add-opens java.base/java.util=ALL-UNNAMED
set CATALINA_OPTS=%CATALINA_OPTS% --add-opens java.base/java.util.concurrent=ALL-UNNAMED
set CATALINA_OPTS=%CATALINA_OPTS% --add-opens java.rmi/sun.rmi.transport=ALL-UNNAMED

rem Configurações de rede
set CATALINA_OPTS=%CATALINA_OPTS% -Djava.net.preferIPv4Stack=true

rem Configurações de encoding
set CATALINA_OPTS=%CATALINA_OPTS% -Dfile.encoding=UTF-8
set CATALINA_OPTS=%CATALINA_OPTS% -Dsun.jnu.encoding=UTF-8

rem Configurações de debug (remover em produção)
rem set CATALINA_OPTS=%CATALINA_OPTS% -Xdebug -Xrunjdwp:transport=dt_socket,address=8000,server=y,suspend=n

echo Configurações CATALINA_OPTS aplicadas:
echo %CATALINA_OPTS%
```

### 8. Instalação como Serviço Windows

```batch
# Instalar Tomcat 10 como serviço
cd "D:\java\apache-tomcat-10.1.15\bin"

# Instalar serviço
service.bat install Tomcat10

# Configurar serviço
# Via services.msc ou:
sc config Tomcat10 start= auto
sc config Tomcat10 DisplayName= "Apache Tomcat 10.1.15"
sc config Tomcat10 description= "Apache Tomcat 10.1.15 - Java 21"

# Iniciar serviço
net start Tomcat10
```

### 9. Testes de Verificação

#### 9.1 Teste Básico de Inicialização

```bash
# Iniciar Tomcat
D:\java\apache-tomcat-10.1.15\bin\startup.bat

# Verificar logs
tail -f D:\java\apache-tomcat-10.1.15\logs\catalina.out

# Verificar se está rodando
netstat -an | findstr :8080
```

#### 9.2 Teste de Acesso Web

```bash
# Acessar página padrão
curl http://localhost:8080

# Ou abrir no navegador:
# http://localhost:8080
# http://localhost:8080/manager/html
```

#### 9.3 Teste de DataSources

```bash
# Verificar logs para erros de DataSource
findstr /i "datasource\|jdbc" D:\java\apache-tomcat-10.1.15\logs\catalina.out
```

### 10. Deploy da Aplicação WebPublication

#### 10.1 Preparar WAR para Tomcat 10

```bash
# Build da aplicação com configurações atualizadas
cd Source
ant clean
ant build

# Verificar se WAR foi gerado
dir dist\*.war
```

#### 10.2 Deploy Manual

```bash
# Copiar WAR para webapps
copy "Source\dist\webp.war" "D:\java\apache-tomcat-10.1.15\webapps\"

# Verificar deploy nos logs
tail -f D:\java\apache-tomcat-10.1.15\logs\localhost.out
```

### 11. Script de Migração Automatizada

#### migrate_tomcat.bat

```batch
@echo off
echo ========================================
echo    MIGRAÇÃO TOMCAT 8.0.53 → 10.1.15
echo ========================================
echo.

echo === PARANDO TOMCAT 8 ===
net stop Tomcat8 2>nul
taskkill /f /im java.exe 2>nul

echo === BACKUP TOMCAT 8 ===
if not exist "backup\tomcat-8.0.53-backup" (
    mkdir "backup\tomcat-8.0.53-backup"
    xcopy "D:\java\apache-tomcat-8.0.53" "backup\tomcat-8.0.53-backup" /E /I /H /Y
    echo ✅ Backup criado
) else (
    echo ✅ Backup já existe
)

echo === CONFIGURANDO TOMCAT 10 ===
setx CATALINA_HOME "D:\java\apache-tomcat-10.1.15" /M
setx CATALINA_BASE "D:\java\apache-tomcat-10.1.15" /M

echo === INSTALANDO SERVIÇO ===
cd "D:\java\apache-tomcat-10.1.15\bin"
service.bat install Tomcat10
sc config Tomcat10 start= auto

echo === INICIANDO TOMCAT 10 ===
net start Tomcat10

echo === VERIFICANDO STATUS ===
timeout /t 30 /nobreak
netstat -an | findstr :8080
if %errorlevel% equ 0 (
    echo ✅ Tomcat 10 rodando na porta 8080
) else (
    echo ❌ Problema na inicialização
)

echo.
echo Migração concluída!
echo Acesse: http://localhost:8080
pause
```

### 12. Troubleshooting

#### Problema: Tomcat não inicia
```bash
# Verificar logs
type D:\java\apache-tomcat-10.1.15\logs\catalina.out

# Verificar Java
java -version

# Verificar porta
netstat -an | findstr :8080
```

#### Problema: DataSource não funciona
```bash
# Verificar drivers no lib
dir D:\java\apache-tomcat-10.1.15\lib\*jdbc*

# Verificar configuração
type D:\java\apache-tomcat-10.1.15\conf\context.xml
```

#### Problema: Aplicação não deploya
```bash
# Verificar logs de deploy
type D:\java\apache-tomcat-10.1.15\logs\localhost.out

# Verificar web.xml
# Deve estar em Servlet 5.0
```

### 13. Checklist de Validação

- [ ] Tomcat 10.1.15 instalado
- [ ] Variáveis de ambiente configuradas
- [ ] Configurações migradas (server.xml, context.xml)
- [ ] Logs configurados
- [ ] Memória e performance configuradas
- [ ] Serviço Windows instalado
- [ ] Tomcat iniciando sem erros
- [ ] Porta 8080 acessível
- [ ] DataSources funcionando
- [ ] Manager app acessível
- [ ] Aplicação WebPublication deployada

## Próximo Passo
**[2.3 Configuração do Eclipse/IDE](./2.3-configuracao-ide.md)**

---
**Status**: ⏳ Pendente
**Responsável**: DevOps/Desenvolvedor
**Estimativa**: 4-6 horas
