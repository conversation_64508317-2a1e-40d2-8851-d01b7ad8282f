# 2.3 Configuração do Eclipse/IDE

## Objetivo
Configurar o Eclipse IDE para trabalhar com Java 21, Tomcat 10.1.15 e as novas dependências da aplicação WebPublication.

## Pré-requisitos
- JDK 21 instalado e configurado
- Tomcat 10.1.15 instalado
- Eclipse IDE instalado (versão 2023-06 ou superior)

## Tempo Estimado
2-3 horas

## Análise da Situação Atual

### **Configuração Atual Identificada:**
- **Eclipse**: Configurado para Java 8
- **JRE**: jdk1.8.0_362
- **Tomcat**: 8.0.53
- **Classpath**: Bibliotecas Java 8
- **Encoding**: Pode estar em ISO-8859-1

### **Problemas com Java 21:**
- JRE desatualizado no Eclipse
- Servidor Tomcat incompatível
- Bibliotecas desatualizadas no classpath
- Possíveis problemas de encoding

## Passos Detalhados

### 1. Verificação da Versão do Eclipse

#### 1.1 Verificar Compatibilidade
```
Eclipse IDE for Enterprise Java and Web Developers
Versão mínima requerida: 2023-06 (4.28)
Versão recomendada: 2023-09 (4.29) ou superior

Para verificar:
Help > About Eclipse IDE
```

#### 1.2 Atualização do Eclipse (se necessário)
```
Se versão < 2023-06:
1. Fazer backup do workspace atual
2. Download da versão mais recente:
   https://www.eclipse.org/downloads/packages/
3. Instalar nova versão
4. Importar workspace existente
```

### 2. Configuração do JDK 21 no Eclipse

#### 2.1 Adicionar JDK 21
```
1. Window > Preferences
2. Java > Installed JREs
3. Add... > Standard VM
4. JRE home: C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot
5. JRE name: OpenJDK 21
6. Finish
7. Marcar como default (checkbox)
8. Apply and Close
```

#### 2.2 Configurar Compiler Compliance
```
1. Window > Preferences
2. Java > Compiler
3. Compiler compliance level: 21
4. Generated .class files compatibility: 21
5. Source compatibility: 21
6. Apply and Close
```

#### 2.3 Configurar Build Path
```
1. Window > Preferences
2. Java > Build Path > Classpath Variables
3. Verificar se JRE_LIB aponta para Java 21
4. Se necessário, editar para apontar para:
   C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\lib
```

### 3. Configuração do Tomcat 10.1.15 no Eclipse

#### 3.1 Remover Tomcat 8 (se existir)
```
1. Window > Preferences
2. Server > Runtime Environments
3. Selecionar Apache Tomcat v8.0
4. Remove
5. Apply
```

#### 3.2 Adicionar Tomcat 10.1
```
1. Window > Preferences
2. Server > Runtime Environments
3. Add...
4. Apache > Apache Tomcat v10.1
5. Next
6. Tomcat installation directory: D:\java\apache-tomcat-10.1.15
7. JRE: OpenJDK 21
8. Finish
9. Apply and Close
```

#### 3.3 Configurar Server no Servers View
```
1. Window > Show View > Servers
2. Click direito na área vazia > New > Server
3. Apache > Tomcat v10.1 Server
4. Server runtime environment: Apache Tomcat v10.1
5. Next
6. Add WebPublication project (se disponível)
7. Finish
```

### 4. Configuração do Projeto WebPublication

#### 4.1 Atualizar .classpath
```xml
<?xml version="1.0" encoding="UTF-8"?>
<classpath>
    <classpathentry kind="src" path="WEB-INF/src"/>
    <classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER/org.eclipse.jdt.internal.debug.ui.launcher.StandardVMType/OpenJDK-21">
        <attributes>
            <attribute name="module" value="true"/>
            <attribute name="owner.project.facets" value="java"/>
        </attributes>
    </classpathentry>
    <classpathentry kind="con" path="org.eclipse.jst.server.core.container/org.eclipse.jst.server.tomcat.runtimeTarget/Apache Tomcat v10.1">
        <attributes>
            <attribute name="owner.project.facets" value="jst.web"/>
        </attributes>
    </classpathentry>
    <classpathentry kind="con" path="org.eclipse.jst.j2ee.internal.web.container"/>
    <classpathentry kind="con" path="org.eclipse.jst.j2ee.internal.module.container"/>
    <classpathentry kind="lib" path="WEB-INF/lib" sourcepath="WEB-INF/lib">
        <attributes>
            <attribute name="org.eclipse.jst.component.dependency" value="/WEB-INF/lib"/>
        </attributes>
    </classpathentry>
    <classpathentry kind="output" path="WEB-INF/classes"/>
</classpath>
```

#### 4.2 Atualizar .project
```xml
<?xml version="1.0" encoding="UTF-8"?>
<projectDescription>
    <name>WebPublication</name>
    <comment></comment>
    <projects>
    </projects>
    <buildSpec>
        <buildCommand>
            <name>org.eclipse.jdt.core.javabuilder</name>
            <arguments>
            </arguments>
        </buildCommand>
        <buildCommand>
            <name>org.eclipse.wst.common.project.facet.core.builder</name>
            <arguments>
            </arguments>
        </buildCommand>
        <buildCommand>
            <name>org.eclipse.wst.validation.validationbuilder</name>
            <arguments>
            </arguments>
        </buildCommand>
    </buildSpec>
    <natures>
        <nature>org.eclipse.jem.workbench.JavaEMFNature</nature>
        <nature>org.eclipse.wst.common.modulecore.ModuleCoreNature</nature>
        <nature>org.eclipse.wst.common.project.facet.core.nature</nature>
        <nature>org.eclipse.jdt.core.javanature</nature>
        <nature>org.eclipse.wst.jsdt.core.jsNature</nature>
    </natures>
</projectDescription>
```

#### 4.3 Configurar Project Facets
```
1. Click direito no projeto > Properties
2. Project Facets
3. Configurar:
   - Java: 21
   - Dynamic Web Module: 5.0
   - JavaScript: 1.0
4. Apply and Close
```

### 5. Configuração de Encoding

#### 5.1 Workspace Encoding
```
1. Window > Preferences
2. General > Workspace
3. Text file encoding: UTF-8
4. Apply
```

#### 5.2 Project Encoding
```
1. Click direito no projeto > Properties
2. Resource
3. Text file encoding: UTF-8
4. Apply and Close
```

#### 5.3 Content Types
```
1. Window > Preferences
2. General > Content Types
3. Text > Java Source File
4. Default encoding: UTF-8
5. Update
6. Repetir para:
   - Text > JSP
   - Text > XML
   - Text > HTML
7. Apply and Close
```

### 6. Configuração de Formatação de Código

#### 6.1 Java Code Style
```
1. Window > Preferences
2. Java > Code Style > Formatter
3. Active profile: Eclipse [built-in]
4. Edit... (criar novo profile)
5. Profile name: WebPublication Java 21
6. Configurações importantes:
   - Indentation > Tab policy: Spaces only
   - Indentation > Indentation size: 4
   - Line Wrapping > Maximum line width: 120
7. OK
8. Apply and Close
```

#### 6.2 Organize Imports
```
1. Window > Preferences
2. Java > Code Style > Organize Imports
3. Configurar ordem:
   - java
   - javax
   - jakarta
   - org
   - com
   - (static imports)
4. Apply and Close
```

### 7. Configuração de Debugging

#### 7.1 Debug Configuration
```
1. Run > Debug Configurations...
2. Apache Tomcat > New Configuration
3. Name: WebPublication Debug
4. Server: Tomcat v10.1 Server
5. Project: WebPublication
6. Arguments tab:
   - VM arguments:
     -Xmx2048m
     -XX:+UseG1GC
     --add-opens java.base/java.lang=ALL-UNNAMED
     --add-opens java.base/java.util=ALL-UNNAMED
7. Apply
8. Close
```

#### 7.2 Hot Code Replace
```
1. Window > Preferences
2. Java > Debug
3. Enable:
   - Hot code replace
   - Show error when hot code replace fails
   - Show error when hot code replace is not supported
4. Apply and Close
```

### 8. Configuração de Plugins Úteis

#### 8.1 Plugins Recomendados
```
Help > Eclipse Marketplace

Instalar:
1. Enhanced Class Decompiler
2. SonarLint
3. SpotBugs Eclipse Plugin
4. EclEmma Java Code Coverage
5. Buildship Gradle Integration (se necessário)
```

#### 8.2 Configurar SonarLint
```
1. Window > Preferences
2. SonarLint > Rules Configuration
3. Ativar regras para Java 21
4. Apply and Close
```

### 9. Configuração de Build Path Libraries

#### 9.1 Verificar Libraries
```
1. Click direito no projeto > Properties
2. Java Build Path > Libraries
3. Verificar se contém:
   - Modulepath: OpenJDK 21
   - Classpath: 
     * Apache Tomcat v10.1
     * Web App Libraries
     * WEB-INF/lib
4. Se necessário, remover referências antigas e adicionar novas
```

#### 9.2 Refresh Libraries
```
1. Click direito no projeto > Refresh
2. Project > Clean... > Clean all projects
3. Aguardar rebuild automático
```

### 10. Configuração de Deployment

#### 10.1 Deployment Assembly
```
1. Click direito no projeto > Properties
2. Deployment Assembly
3. Verificar entradas:
   - Source: WEB-INF/src → Deploy Path: WEB-INF/classes
   - Source: WEB-INF/lib → Deploy Path: WEB-INF/lib
   - Source: WebContent → Deploy Path: /
4. Se necessário, remover/adicionar entradas
5. Apply and Close
```

#### 10.2 Server Configuration
```
1. Servers view > Double-click Tomcat v10.1 Server
2. Server Locations:
   - Use workspace metadata (recomendado para desenvolvimento)
3. Publishing:
   - Automatically publish when resources change
4. Timeouts:
   - Start: 45 seconds
   - Stop: 15 seconds
5. Save (Ctrl+S)
```

### 11. Testes de Configuração

#### 11.1 Teste de Compilação
```
1. Project > Clean...
2. Clean all projects
3. Build automatically
4. Verificar Problems view para erros
```

#### 11.2 Teste de Deploy
```
1. Click direito no projeto > Run As > Run on Server
2. Selecionar Tomcat v10.1 Server
3. Finish
4. Verificar Console para erros
5. Abrir http://localhost:8080/WebPublication
```

#### 11.3 Teste de Debug
```
1. Colocar breakpoint em alguma classe
2. Click direito no projeto > Debug As > Debug on Server
3. Acessar aplicação para ativar breakpoint
4. Verificar se debug funciona corretamente
```

### 12. Script de Configuração Automatizada

#### configure_eclipse.bat
```batch
@echo off
echo ========================================
echo    CONFIGURAÇÃO ECLIPSE PARA JAVA 21
echo ========================================
echo.

echo === BACKUP WORKSPACE ===
if not exist "backup\eclipse-workspace" (
    mkdir "backup\eclipse-workspace"
    xcopy "%USERPROFILE%\eclipse-workspace\.metadata" "backup\eclipse-workspace\.metadata" /E /I /H /Y
    echo ✅ Backup do workspace criado
)

echo === CONFIGURAÇÕES RECOMENDADAS ===
echo.
echo 1. Configurar JDK 21 como default
echo 2. Adicionar Tomcat 10.1.15
echo 3. Configurar encoding UTF-8
echo 4. Atualizar project facets
echo 5. Configurar debug settings
echo.
echo Execute os passos manuais conforme documentação.
echo.

echo === VERIFICAÇÃO AUTOMÁTICA ===
if exist "%USERPROFILE%\eclipse-workspace\WebPublication\.classpath" (
    echo ✅ Projeto WebPublication encontrado
    
    findstr "OpenJDK-21" "%USERPROFILE%\eclipse-workspace\WebPublication\.classpath" >nul
    if %errorlevel% equ 0 (
        echo ✅ JDK 21 configurado no projeto
    ) else (
        echo ⚠️ JDK 21 não configurado no projeto
    )
    
    findstr "tomcat.*10" "%USERPROFILE%\eclipse-workspace\WebPublication\.classpath" >nul
    if %errorlevel% equ 0 (
        echo ✅ Tomcat 10 configurado no projeto
    ) else (
        echo ⚠️ Tomcat 10 não configurado no projeto
    )
) else (
    echo ⚠️ Projeto WebPublication não encontrado no workspace
)

echo.
echo Configuração concluída!
echo Abra o Eclipse e verifique as configurações.
pause
```

### 13. Troubleshooting

#### Problema: Projeto não compila
```
Soluções:
1. Project > Clean... > Clean all projects
2. Verificar JDK 21 nas preferências
3. Verificar .classpath do projeto
4. Refresh projeto (F5)
```

#### Problema: Tomcat não inicia no Eclipse
```
Soluções:
1. Verificar se Tomcat 10.1 está configurado
2. Verificar se JDK 21 está selecionado para o server
3. Limpar workspace do server:
   - Servers view > Delete server
   - Recriar server
```

#### Problema: Encoding incorreto
```
Soluções:
1. Verificar encoding do workspace (UTF-8)
2. Verificar encoding do projeto (UTF-8)
3. Verificar content types (UTF-8)
4. Refresh projeto
```

#### Problema: Debug não funciona
```
Soluções:
1. Verificar se projeto está deployado em modo debug
2. Verificar VM arguments do debug configuration
3. Verificar se hot code replace está habilitado
4. Restart do server em debug mode
```

### 14. Configurações Avançadas

#### 14.1 Memory Settings para Eclipse
```
Editar eclipse.ini:
-vmargs
-Xms1024m
-Xmx4096m
-XX:+UseG1GC
-XX:+UseStringDeduplication
--add-modules=ALL-SYSTEM
```

#### 14.2 Workspace Settings
```
1. Window > Preferences
2. General > Startup and Shutdown
3. Refresh workspace on startup: ✓
4. Confirm exit when closing last window: ✓
5. Apply and Close
```

### 15. Checklist de Validação

#### Eclipse Configuration:
- [ ] Eclipse versão 2023-06 ou superior
- [ ] JDK 21 configurado como default
- [ ] Compiler compliance level: 21
- [ ] Encoding UTF-8 configurado

#### Tomcat Configuration:
- [ ] Tomcat 10.1.15 adicionado ao Eclipse
- [ ] Server runtime configurado
- [ ] JDK 21 selecionado para o server
- [ ] Deploy automático habilitado

#### Project Configuration:
- [ ] .classpath atualizado para Java 21
- [ ] Project facets atualizados
- [ ] Build path correto
- [ ] Deployment assembly configurado

#### Testing:
- [ ] Projeto compila sem erros
- [ ] Deploy no Tomcat funciona
- [ ] Debug mode funciona
- [ ] Hot code replace funciona
- [ ] Aplicação acessível via browser

## Próximo Passo
**[2.4 Atualização do Ant Build](./2.4-atualizacao-ant.md)**

---
**Status**: ⏳ Pendente
**Responsável**: Desenvolvedor
**Estimativa**: 2-3 horas
