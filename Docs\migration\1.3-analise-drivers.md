# 1.3 An<PERSON><PERSON><PERSON> de Drivers de Banco de Dados

## Objetivo
Verificar compatibilidade dos drivers de banco de dados atuais com Java 21 e identificar versões compatíveis necessárias.

## Pré-requisitos
- Inventário de dependências concluído (1.1)
- Análise de compatibilidade iniciada (1.2)
- Acesso aos arquivos de configuração de banco

## Tempo Estimado
4-6 horas

## Bancos de Dados Identificados

### **Configurações Atuais (baseado em context.xml):**

#### **1. Oracle Database**
- **Ambiente**: CRM Production
- **Host**: **************:1521
- **Database**: MORALPDB
- **Schema**: WEBP_CRM_DEV
- **Driver Atual**: Provavelmente ojdbc6.jar ou ojdbc7.jar

#### **2. MySQL**
- **Ambiente**: Visionnaire Development
- **Host**: **************:3306
- **Database**: dev_visionnaire
- **Driver Atual**: mysql-connector-java-5.x.jar

#### **3. SQL Server**
- **Ambiente**: FIEPE Development
- **Host**: **************:1433
- **Database**: dev_fiepe
- **Driver Atual**: sqljdbc4.jar ou sqljdbc6.jar

#### **4. PostgreSQL**
- **Ambiente**: CITS Production
- **Host**: **************:5432
- **Database**: prod_cits
- **Driver Atual**: postgresql-9.x.jar

## Análise de Compatibilidade por Driver

### 1. Oracle JDBC Driver

#### **Situação Atual:**
```bash
# Verificar driver atual
dir "Source\WEB-INF\lib\ojdbc*.jar"
# Possíveis versões: ojdbc6.jar, ojdbc7.jar, ojdbc8.jar
```

#### **Compatibilidade com Java 21:**
| Driver Version | Java 8 | Java 11 | Java 17 | Java 21 | Status |
|----------------|---------|---------|---------|---------|---------|
| ojdbc6.jar | ✅ | ❌ | ❌ | ❌ | Incompatível |
| ojdbc7.jar | ✅ | ❌ | ❌ | ❌ | Incompatível |
| ojdbc8.jar | ✅ | ✅ | ❌ | ❌ | Incompatível |
| ojdbc10.jar | ✅ | ✅ | ✅ | ❌ | Incompatível |
| ojdbc11.jar | ✅ | ✅ | ✅ | ✅ | **Compatível** |

#### **Versão Recomendada:**
- **ojdbc11.jar** (versão ******** ou superior)
- **Download**: Oracle Technology Network
- **Licença**: Oracle License Agreement

#### **Configuração Atualizada:**
```xml
<!-- context.xml -->
<Resource name="jdbc/WebpCrmDev"
          auth="Container"
          type="javax.sql.DataSource"
          driverClassName="oracle.jdbc.OracleDriver"
          url="**********************************************"
          username="WEBP_CRM_DEV"
          password="mSKqzKHzdDJk"
          maxTotal="20"
          maxIdle="10"
          maxWaitMillis="10000"
          testOnBorrow="true"
          validationQuery="SELECT 1 FROM DUAL" />
```

### 2. MySQL Connector/J

#### **Situação Atual:**
```bash
# Verificar driver atual
dir "Source\WEB-INF\lib\mysql-connector-java*.jar"
# Possível versão: mysql-connector-java-5.1.x.jar
```

#### **Compatibilidade com Java 21:**
| Driver Version | Java 8 | Java 11 | Java 17 | Java 21 | Status |
|----------------|---------|---------|---------|---------|---------|
| 5.1.x | ✅ | ❌ | ❌ | ❌ | Incompatível |
| 6.0.x | ✅ | ❌ | ❌ | ❌ | Incompatível |
| 8.0.x | ✅ | ✅ | ✅ | ✅ | **Compatível** |
| 8.1.x | ✅ | ✅ | ✅ | ✅ | **Compatível** |

#### **Versão Recomendada:**
- **mysql-connector-j-8.1.0.jar** (nova nomenclatura)
- **Download**: MySQL Official Site
- **Licença**: GPL v2 with FOSS License Exception

#### **Mudanças Importantes:**
```java
// Driver class permanece o mesmo
driverClassName="com.mysql.cj.jdbc.Driver"

// URL precisa de parâmetros adicionais
url="********************************************************************************&allowPublicKeyRetrieval=true"
```

#### **Configuração Atualizada:**
```xml
<Resource name="jdbc/VisionnaireDevMysql"
          auth="Container"
          type="javax.sql.DataSource"
          driverClassName="com.mysql.cj.jdbc.Driver"
          url="*********************************************************************************************************************"
          username="root"
          password="a4DqYcBvKB"
          maxTotal="20"
          maxIdle="10"
          maxWaitMillis="10000"
          testOnBorrow="true"
          validationQuery="SELECT 1" />
```

### 3. Microsoft SQL Server JDBC Driver

#### **Situação Atual:**
```bash
# Verificar driver atual
dir "Source\WEB-INF\lib\sqljdbc*.jar"
# Possíveis versões: sqljdbc4.jar, sqljdbc6.jar
```

#### **Compatibilidade com Java 21:**
| Driver Version | Java 8 | Java 11 | Java 17 | Java 21 | Status |
|----------------|---------|---------|---------|---------|---------|
| sqljdbc4.jar | ✅ | ❌ | ❌ | ❌ | Incompatível |
| sqljdbc6.jar | ✅ | ❌ | ❌ | ❌ | Incompatível |
| mssql-jdbc-7.x | ✅ | ✅ | ❌ | ❌ | Incompatível |
| mssql-jdbc-9.x | ✅ | ✅ | ✅ | ❌ | Incompatível |
| mssql-jdbc-12.x | ✅ | ✅ | ✅ | ✅ | **Compatível** |

#### **Versão Recomendada:**
- **mssql-jdbc-12.4.1.jre11.jar**
- **Download**: Microsoft Download Center
- **Licença**: MIT License

#### **Configuração Atualizada:**
```xml
<Resource name="jdbc/FiepeDevSqlserver"
          auth="Container"
          type="javax.sql.DataSource"
          driverClassName="com.microsoft.sqlserver.jdbc.SQLServerDriver"
          url="*************************************************************************;trustServerCertificate=true"
          username="sa"
          password="a4DqYcBvKB"
          maxTotal="20"
          maxIdle="10"
          maxWaitMillis="10000"
          testOnBorrow="true"
          validationQuery="SELECT 1" />
```

### 4. PostgreSQL JDBC Driver

#### **Situação Atual:**
```bash
# Verificar driver atual
dir "Source\WEB-INF\lib\postgresql*.jar"
# Possível versão: postgresql-9.x.jar
```

#### **Compatibilidade com Java 21:**
| Driver Version | Java 8 | Java 11 | Java 17 | Java 21 | Status |
|----------------|---------|---------|---------|---------|---------|
| 9.4.x | ✅ | ❌ | ❌ | ❌ | Incompatível |
| 42.2.x | ✅ | ✅ | ❌ | ❌ | Incompatível |
| 42.5.x | ✅ | ✅ | ✅ | ❌ | Incompatível |
| 42.6.x | ✅ | ✅ | ✅ | ✅ | **Compatível** |

#### **Versão Recomendada:**
- **postgresql-42.6.0.jar**
- **Download**: PostgreSQL JDBC Site
- **Licença**: BSD License

#### **Configuração Atualizada:**
```xml
<Resource name="jdbc/CitsProdPostgresql"
          auth="Container"
          type="javax.sql.DataSource"
          driverClassName="org.postgresql.Driver"
          url="***********************************************?ssl=false"
          username="postgres"
          password="a4DqYcBvKB"
          maxTotal="20"
          maxIdle="10"
          maxWaitMillis="10000"
          testOnBorrow="true"
          validationQuery="SELECT 1" />
```

## Script de Análise de Drivers

### analyze_drivers.ps1
```powershell
# Script para analisar drivers de banco atuais
Write-Host "=== ANÁLISE DE DRIVERS DE BANCO ===" -ForegroundColor Yellow
Write-Host ""

$libPath = "Source\WEB-INF\lib"
$driversFound = @()

# Verificar Oracle
$oracleDrivers = Get-ChildItem "$libPath\ojdbc*.jar" -ErrorAction SilentlyContinue
if ($oracleDrivers) {
    foreach ($driver in $oracleDrivers) {
        $driversFound += [PSCustomObject]@{
            Database = "Oracle"
            Driver = $driver.Name
            Status = if ($driver.Name -match "ojdbc11") { "✅ Compatível" } else { "❌ Incompatível" }
            Recommendation = "ojdbc11.jar (********+)"
        }
    }
} else {
    Write-Host "⚠️ Nenhum driver Oracle encontrado" -ForegroundColor Yellow
}

# Verificar MySQL
$mysqlDrivers = Get-ChildItem "$libPath\mysql-connector*.jar" -ErrorAction SilentlyContinue
if ($mysqlDrivers) {
    foreach ($driver in $mysqlDrivers) {
        $driversFound += [PSCustomObject]@{
            Database = "MySQL"
            Driver = $driver.Name
            Status = if ($driver.Name -match "8\.[01]") { "✅ Compatível" } else { "❌ Incompatível" }
            Recommendation = "mysql-connector-j-8.1.0.jar"
        }
    }
} else {
    Write-Host "⚠️ Nenhum driver MySQL encontrado" -ForegroundColor Yellow
}

# Verificar SQL Server
$sqlserverDrivers = Get-ChildItem "$libPath\*sql*.jar" -ErrorAction SilentlyContinue
if ($sqlserverDrivers) {
    foreach ($driver in $sqlserverDrivers) {
        $driversFound += [PSCustomObject]@{
            Database = "SQL Server"
            Driver = $driver.Name
            Status = if ($driver.Name -match "mssql-jdbc-1[2-9]") { "✅ Compatível" } else { "❌ Incompatível" }
            Recommendation = "mssql-jdbc-12.4.1.jre11.jar"
        }
    }
} else {
    Write-Host "⚠️ Nenhum driver SQL Server encontrado" -ForegroundColor Yellow
}

# Verificar PostgreSQL
$postgresDrivers = Get-ChildItem "$libPath\postgresql*.jar" -ErrorAction SilentlyContinue
if ($postgresDrivers) {
    foreach ($driver in $postgresDrivers) {
        $driversFound += [PSCustomObject]@{
            Database = "PostgreSQL"
            Driver = $driver.Name
            Status = if ($driver.Name -match "42\.6") { "✅ Compatível" } else { "❌ Incompatível" }
            Recommendation = "postgresql-42.6.0.jar"
        }
    }
} else {
    Write-Host "⚠️ Nenhum driver PostgreSQL encontrado" -ForegroundColor Yellow
}

# Exibir resultados
if ($driversFound.Count -gt 0) {
    Write-Host "DRIVERS ENCONTRADOS:" -ForegroundColor Cyan
    $driversFound | Format-Table -AutoSize
    
    $incompatible = $driversFound | Where-Object { $_.Status -match "❌" }
    if ($incompatible.Count -gt 0) {
        Write-Host ""
        Write-Host "⚠️ AÇÃO NECESSÁRIA: $($incompatible.Count) driver(s) incompatível(is) encontrado(s)" -ForegroundColor Red
        Write-Host "Atualize os drivers antes de prosseguir com a migração Java 21" -ForegroundColor Red
    } else {
        Write-Host ""
        Write-Host "✅ Todos os drivers são compatíveis com Java 21!" -ForegroundColor Green
    }
} else {
    Write-Host "❌ Nenhum driver de banco encontrado!" -ForegroundColor Red
}

Write-Host ""
Write-Host "📋 Próximos passos:" -ForegroundColor Cyan
Write-Host "1. Baixar drivers compatíveis" -ForegroundColor White
Write-Host "2. Atualizar WEB-INF/lib/" -ForegroundColor White
Write-Host "3. Atualizar configurações de conexão" -ForegroundColor White
Write-Host "4. Testar conectividade" -ForegroundColor White
```

## Plano de Atualização de Drivers

### Fase 1: Download dos Drivers Compatíveis

#### Oracle ojdbc11.jar
```bash
# Download manual necessário (requer conta Oracle)
# URL: https://www.oracle.com/database/technologies/appdev/jdbc-downloads.html
# Arquivo: ojdbc11-********.jar
```

#### MySQL Connector/J 8.1.0
```bash
# Download direto
$url = "https://dev.mysql.com/get/Downloads/Connector-J/mysql-connector-j-8.1.0.zip"
Invoke-WebRequest -Uri $url -OutFile "mysql-connector-j-8.1.0.zip"
```

#### SQL Server JDBC 12.4.1
```bash
# Download direto
$url = "https://download.microsoft.com/download/3/a/e/3ae6c5ce-9677-4d8d-8bfe-46624b61b0b6/sqljdbc_12.4.1.0_enu.zip"
Invoke-WebRequest -Uri $url -OutFile "sqljdbc_12.4.1.0_enu.zip"
```

#### PostgreSQL JDBC 42.6.0
```bash
# Download direto
$url = "https://jdbc.postgresql.org/download/postgresql-42.6.0.jar"
Invoke-WebRequest -Uri $url -OutFile "postgresql-42.6.0.jar"
```

### Fase 2: Script de Atualização

#### update_drivers.bat
```batch
@echo off
echo ========================================
echo    ATUALIZAÇÃO DE DRIVERS DE BANCO
echo ========================================
echo.

echo === BACKUP DOS DRIVERS ATUAIS ===
mkdir "backup\drivers-java8" 2>nul
copy "Source\WEB-INF\lib\ojdbc*.jar" "backup\drivers-java8\" 2>nul
copy "Source\WEB-INF\lib\mysql-connector*.jar" "backup\drivers-java8\" 2>nul
copy "Source\WEB-INF\lib\*sql*.jar" "backup\drivers-java8\" 2>nul
copy "Source\WEB-INF\lib\postgresql*.jar" "backup\drivers-java8\" 2>nul
echo ✅ Backup criado

echo.
echo === REMOVENDO DRIVERS ANTIGOS ===
del "Source\WEB-INF\lib\ojdbc6.jar" 2>nul
del "Source\WEB-INF\lib\ojdbc7.jar" 2>nul
del "Source\WEB-INF\lib\ojdbc8.jar" 2>nul
del "Source\WEB-INF\lib\mysql-connector-java-5*.jar" 2>nul
del "Source\WEB-INF\lib\sqljdbc*.jar" 2>nul
del "Source\WEB-INF\lib\postgresql-9*.jar" 2>nul
del "Source\WEB-INF\lib\postgresql-42.[0-5]*.jar" 2>nul
echo ✅ Drivers antigos removidos

echo.
echo === INSTALANDO DRIVERS JAVA 21 ===
if exist "temp\drivers\ojdbc11.jar" (
    copy "temp\drivers\ojdbc11.jar" "Source\WEB-INF\lib\"
    echo ✅ Oracle driver atualizado
) else (
    echo ⚠️ Oracle driver não encontrado em temp\drivers\
)

if exist "temp\drivers\mysql-connector-j-8.1.0.jar" (
    copy "temp\drivers\mysql-connector-j-8.1.0.jar" "Source\WEB-INF\lib\"
    echo ✅ MySQL driver atualizado
) else (
    echo ⚠️ MySQL driver não encontrado em temp\drivers\
)

if exist "temp\drivers\mssql-jdbc-12.4.1.jre11.jar" (
    copy "temp\drivers\mssql-jdbc-12.4.1.jre11.jar" "Source\WEB-INF\lib\"
    echo ✅ SQL Server driver atualizado
) else (
    echo ⚠️ SQL Server driver não encontrado em temp\drivers\
)

if exist "temp\drivers\postgresql-42.6.0.jar" (
    copy "temp\drivers\postgresql-42.6.0.jar" "Source\WEB-INF\lib\"
    echo ✅ PostgreSQL driver atualizado
) else (
    echo ⚠️ PostgreSQL driver não encontrado em temp\drivers\
)

echo.
echo === VERIFICAÇÃO ===
echo Drivers atuais:
dir "Source\WEB-INF\lib\ojdbc*.jar" 2>nul
dir "Source\WEB-INF\lib\mysql-connector*.jar" 2>nul
dir "Source\WEB-INF\lib\mssql-jdbc*.jar" 2>nul
dir "Source\WEB-INF\lib\postgresql*.jar" 2>nul

echo.
echo ✅ Atualização de drivers concluída!
echo.
echo 📋 Próximos passos:
echo 1. Atualizar configurações de conexão
echo 2. Testar conectividade com cada banco
echo 3. Verificar logs para warnings
echo.
pause
```

## Testes de Conectividade

### test_database_connectivity.java
```java
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

public class TestDatabaseConnectivity {
    
    public static void main(String[] args) {
        System.out.println("=== TESTE DE CONECTIVIDADE DE BANCO ===");
        System.out.println();
        
        // Teste Oracle
        testOracle();
        
        // Teste MySQL
        testMySQL();
        
        // Teste SQL Server
        testSQLServer();
        
        // Teste PostgreSQL
        testPostgreSQL();
    }
    
    private static void testOracle() {
        System.out.println("Testando Oracle...");
        try {
            Class.forName("oracle.jdbc.OracleDriver");
            Connection conn = DriverManager.getConnection(
                "**********************************************",
                "WEBP_CRM_DEV", "mSKqzKHzdDJk");
            System.out.println("✅ Oracle: Conexão bem-sucedida");
            conn.close();
        } catch (Exception e) {
            System.out.println("❌ Oracle: " + e.getMessage());
        }
        System.out.println();
    }
    
    private static void testMySQL() {
        System.out.println("Testando MySQL...");
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
            Connection conn = DriverManager.getConnection(
                "********************************************************************************",
                "root", "a4DqYcBvKB");
            System.out.println("✅ MySQL: Conexão bem-sucedida");
            conn.close();
        } catch (Exception e) {
            System.out.println("❌ MySQL: " + e.getMessage());
        }
        System.out.println();
    }
    
    private static void testSQLServer() {
        System.out.println("Testando SQL Server...");
        try {
            Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
            Connection conn = DriverManager.getConnection(
                "*************************************************************************",
                "sa", "a4DqYcBvKB");
            System.out.println("✅ SQL Server: Conexão bem-sucedida");
            conn.close();
        } catch (Exception e) {
            System.out.println("❌ SQL Server: " + e.getMessage());
        }
        System.out.println();
    }
    
    private static void testPostgreSQL() {
        System.out.println("Testando PostgreSQL...");
        try {
            Class.forName("org.postgresql.Driver");
            Connection conn = DriverManager.getConnection(
                "***********************************************",
                "postgres", "a4DqYcBvKB");
            System.out.println("✅ PostgreSQL: Conexão bem-sucedida");
            conn.close();
        } catch (Exception e) {
            System.out.println("❌ PostgreSQL: " + e.getMessage());
        }
        System.out.println();
    }
}
```

## Checklist de Validação

### Análise Completa:
- [ ] Drivers atuais identificados
- [ ] Compatibilidade com Java 21 verificada
- [ ] Versões compatíveis identificadas
- [ ] URLs de download coletadas

### Atualização:
- [ ] Backup dos drivers atuais criado
- [ ] Drivers compatíveis baixados
- [ ] Drivers antigos removidos
- [ ] Drivers novos instalados

### Configuração:
- [ ] URLs de conexão atualizadas
- [ ] Parâmetros de conexão ajustados
- [ ] Configurações de pool atualizadas
- [ ] Validação queries configuradas

### Testes:
- [ ] Conectividade Oracle testada
- [ ] Conectividade MySQL testada
- [ ] Conectividade SQL Server testada
- [ ] Conectividade PostgreSQL testada

## Próximo Passo
**[1.4 Backup e Controle de Versão](./1.4-backup-controle-versao.md)**

---
**Status**: ⏳ Pendente
**Responsável**: DBA/Desenvolvedor Senior
**Estimativa**: 4-6 horas
