# Plano de Rollback - Migração Java 8 para Java 21

## Visão Geral

Este documento define os procedimentos para reverter a migração Java 21 e retornar ao estado Java 8 em caso de problemas críticos.

---

## Critérios para Rollback

### Executar Rollback Imediatamente Se:

#### <PERSON><PERSON><PERSON><PERSON><PERSON> (Rollback Obrigatório)
- [ ] Falhas de compilação não resolvidas em 4 horas
- [ ] Aplicação não inicia após 2 tentativas
- [ ] Perda total de conectividade com banco de dados
- [ ] Corrupção de dados detectada
- [ ] Falhas de segurança críticas
- [ ] Sistema completamente inacessível

#### Severos (Rollback Recomendado)
- [ ] Degradação de performance > 50%
- [ ] Funcionalidades críticas não funcionam
- [ ] Erros frequentes em logs (> 100/min)
- [ ] Memory leaks severos
- [ ] Timeouts constantes
- [ ] Instabilidade geral do sistema

#### Moderados (Avaliar Rollback)
- [ ] Degradação de performance 20-50%
- [ ] Funcionalidades secundárias com problemas
- [ ] Warnings excessivos em logs
- [ ] Problemas de usabilidade
- [ ] Reclamações de usuários

---

## Procedimentos de Rollback

### Rollback Nível 1: Código Fonte

#### Tempo Estimado: 30 minutos

```bash
# 1. Voltar para branch principal
git checkout main

# 2. Ou voltar para tag de baseline
git checkout java8-baseline

# 3. Ou reverter commits específicos
git revert <commit-hash-java21-migration>

# 4. Verificar estado
git status
git log --oneline -5
```

#### Validação:
```bash
# Verificar se código está no estado Java 8
grep -r "jakarta\." Source/WEB-INF/src/ || echo "Nenhum jakarta encontrado - OK"
grep -r "import java\." Source/WEB-INF/src/ | head -5
```

### Rollback Nível 2: Dependências

#### Tempo Estimado: 1 hora

```bash
# 1. Restaurar bibliotecas originais
echo "Restaurando bibliotecas Java 8..."
del "Source\WEB-INF\lib\*"
copy "backup\lib-java8\*" "Source\WEB-INF\lib\"

# 2. Verificar bibliotecas restauradas
dir "Source\WEB-INF\lib\commons-lang-2.6.jar"
dir "Source\WEB-INF\lib\jackson-databind-2.6.2.jar"
dir "Source\WEB-INF\lib\log4j*.jar"

# 3. Remover configurações Java 21
del "Source\WEB-INF\classes\log4j2.xml"
```

#### Script Automatizado:
```batch
@echo off
echo === ROLLBACK DE DEPENDENCIAS ===
echo.

echo Removendo bibliotecas Java 21...
del "Source\WEB-INF\lib\commons-lang3-*.jar"
del "Source\WEB-INF\lib\jackson-*-2.15.*.jar"
del "Source\WEB-INF\lib\log4j-*-2.*.jar"
del "Source\WEB-INF\lib\jakarta.*.jar"

echo Restaurando bibliotecas Java 8...
copy "backup\lib-java8\*" "Source\WEB-INF\lib\"

echo Verificando restauração...
if exist "Source\WEB-INF\lib\commons-lang-2.6.jar" (
    echo ✅ Commons Lang 2.6 restaurado
) else (
    echo ❌ Erro: Commons Lang não encontrado
)

if exist "Source\WEB-INF\lib\jackson-databind-2.6.2.jar" (
    echo ✅ Jackson 2.6 restaurado
) else (
    echo ❌ Erro: Jackson não encontrado
)

echo Rollback de dependências concluído.
pause
```

### Rollback Nível 3: Configurações

#### Tempo Estimado: 45 minutos

```bash
# 1. Restaurar web.xml original
copy "backup\webxml-configs\web-original.xml" "Source\WEB-INF\web.xml"

# 2. Restaurar context.xml
copy "backup\context-configs\context-original.xml" "Source\META-INF\context.xml"

# 3. Restaurar build.xml
copy "backup\build-configs\build-original.xml" "Source\build.xml"

# 4. Restaurar ant.properties
copy "backup\build-configs\ant-original.properties" "Source\ant.properties"
```

#### Validação de Configurações:
```bash
# Verificar web.xml
grep "version=\"2.4\"" Source/WEB-INF/web.xml
grep "javax.servlet" Source/WEB-INF/web.xml

# Verificar context.xml
grep "javax.sql.DataSource" Source/META-INF/context.xml
```

### Rollback Nível 4: Ambiente Java

#### Tempo Estimado: 1 hora

```bash
# 1. Restaurar Java 8
set JAVA_HOME=C:\Program Files\Java\jdk1.8.0_362
set PATH=C:\Program Files\Java\jdk1.8.0_362\bin;%PATH%

# 2. Ou usar script de alternância
switch_java.bat 8

# 3. Verificar versão
java -version
# Deve mostrar: java version "1.8.0_362"
```

#### Restaurar Tomcat 8:
```bash
# 1. Parar Tomcat 10
net stop Tomcat10

# 2. Restaurar Tomcat 8.0.53
set CATALINA_HOME=D:\java\apache-tomcat-8.0.53
set CATALINA_BASE=D:\java\apache-tomcat-8.0.53

# 3. Restaurar configurações
copy "backup\tomcat-configs\server.xml" "%CATALINA_HOME%\conf\"
copy "backup\tomcat-configs\context.xml" "%CATALINA_HOME%\conf\"

# 4. Iniciar Tomcat 8
net start Tomcat8
```

### Rollback Nível 5: Banco de Dados

#### Tempo Estimado: 2-4 horas (dependendo do tamanho)

#### Oracle:
```sql
-- 1. Parar aplicação
-- 2. Restaurar dump
impdp WEBP_CRM_DEV/password@database \
  directory=BACKUP_DIR \
  dumpfile=webp_crm_dev_java8_baseline.dmp \
  table_exists_action=replace

-- 3. Verificar integridade
SELECT COUNT(*) FROM user_tables;
```

#### MySQL:
```bash
# 1. Parar aplicação
# 2. Restaurar dump
mysql -h localhost -u root -p dev_visionnaire < webp_visionnaire_dev_java8_baseline.sql

# 3. Verificar integridade
mysql -h localhost -u root -p -e "SHOW TABLES;" dev_visionnaire
```

#### SQL Server:
```sql
-- 1. Parar aplicação
-- 2. Restaurar backup
RESTORE DATABASE WEBP_DEV 
FROM DISK = 'C:\Backup\WEBP_DEV_java8_baseline.bak'
WITH REPLACE;

-- 3. Verificar integridade
DBCC CHECKDB('WEBP_DEV');
```

---

## Scripts de Rollback Automatizado

### rollback_complete.bat
```batch
@echo off
echo ========================================
echo    ROLLBACK COMPLETO JAVA 21 → JAVA 8
echo ========================================
echo.
echo ATENÇÃO: Este script irá reverter TODAS as mudanças da migração Java 21
echo.
set /p confirm="Tem certeza que deseja continuar? (S/N): "
if /i not "%confirm%"=="S" goto :end

echo.
echo === FASE 1: PARANDO SERVIÇOS ===
net stop Tomcat10 2>nul
taskkill /f /im java.exe 2>nul

echo.
echo === FASE 2: ROLLBACK DE CÓDIGO ===
cd /d "d:\Visionnaire-RepGit\Visionnaire-WebPublication"
git checkout java8-baseline
if %errorlevel% neq 0 (
    echo ❌ Erro no rollback do código
    goto :error
)
echo ✅ Código restaurado para Java 8

echo.
echo === FASE 3: ROLLBACK DE DEPENDÊNCIAS ===
call rollback_dependencies.bat
if %errorlevel% neq 0 (
    echo ❌ Erro no rollback de dependências
    goto :error
)

echo.
echo === FASE 4: ROLLBACK DE CONFIGURAÇÕES ===
call rollback_configurations.bat
if %errorlevel% neq 0 (
    echo ❌ Erro no rollback de configurações
    goto :error
)

echo.
echo === FASE 5: ROLLBACK DE AMBIENTE ===
call switch_java.bat 8
set CATALINA_HOME=D:\java\apache-tomcat-8.0.53

echo.
echo === FASE 6: INICIANDO SERVIÇOS ===
net start Tomcat8
if %errorlevel% neq 0 (
    echo ❌ Erro ao iniciar Tomcat 8
    goto :error
)

echo.
echo === VALIDAÇÃO ===
timeout /t 30 /nobreak
curl -s http://localhost:8080/webp/ > nul
if %errorlevel% equ 0 (
    echo ✅ Aplicação respondendo
) else (
    echo ⚠️ Aplicação pode não estar respondendo
)

echo.
echo ========================================
echo    ROLLBACK CONCLUÍDO COM SUCESSO
echo ========================================
echo.
echo Próximos passos:
echo 1. Verificar logs da aplicação
echo 2. Testar funcionalidades críticas
echo 3. Notificar equipe sobre rollback
echo 4. Analisar causa raiz dos problemas
goto :end

:error
echo.
echo ========================================
echo    ERRO DURANTE ROLLBACK
echo ========================================
echo.
echo Ações necessárias:
echo 1. Verificar logs de erro
echo 2. Executar rollback manual
echo 3. Contatar equipe de suporte
echo 4. Considerar restauração completa

:end
pause
```

### rollback_dependencies.bat
```batch
@echo off
echo Executando rollback de dependências...

echo Removendo bibliotecas Java 21...
del "Source\WEB-INF\lib\*jakarta*" 2>nul
del "Source\WEB-INF\lib\commons-lang3-*" 2>nul
del "Source\WEB-INF\lib\jackson-*-2.15.*" 2>nul
del "Source\WEB-INF\lib\log4j-*-2.*" 2>nul

echo Restaurando bibliotecas Java 8...
if not exist "backup\lib-java8\" (
    echo ❌ Backup de bibliotecas não encontrado!
    exit /b 1
)

copy "backup\lib-java8\*" "Source\WEB-INF\lib\" >nul
if %errorlevel% neq 0 (
    echo ❌ Erro ao restaurar bibliotecas
    exit /b 1
)

echo ✅ Dependências restauradas
exit /b 0
```

---

## Validação Pós-Rollback

### Checklist de Validação

#### Ambiente:
- [ ] Java 8 ativo (`java -version`)
- [ ] JAVA_HOME correto
- [ ] Tomcat 8 rodando
- [ ] Variáveis de ambiente restauradas

#### Código:
- [ ] Branch/tag Java 8 ativo
- [ ] Imports javax.* presentes
- [ ] Imports jakarta.* removidos
- [ ] Compilação bem-sucedida

#### Dependências:
- [ ] Bibliotecas Java 8 restauradas
- [ ] Bibliotecas Java 21 removidas
- [ ] Sem conflitos de versão
- [ ] Classpath correto

#### Configurações:
- [ ] web.xml Servlet 2.4
- [ ] context.xml original
- [ ] build.xml original
- [ ] Propriedades restauradas

#### Funcionalidade:
- [ ] Aplicação inicia sem erros
- [ ] Login funciona
- [ ] Conectividade com banco OK
- [ ] Funcionalidades críticas OK
- [ ] Performance normal

### Scripts de Validação

#### validate_rollback.ps1:
```powershell
Write-Host "=== VALIDAÇÃO PÓS-ROLLBACK ===" -ForegroundColor Yellow

# Verificar Java
$javaVersion = java -version 2>&1 | Select-String "1.8"
if ($javaVersion) {
    Write-Host "✅ Java 8 ativo" -ForegroundColor Green
} else {
    Write-Host "❌ Java 8 não encontrado" -ForegroundColor Red
}

# Verificar bibliotecas
$lang2 = Test-Path "Source\WEB-INF\lib\commons-lang-2.6.jar"
$jackson26 = Test-Path "Source\WEB-INF\lib\jackson-databind-2.6.2.jar"

if ($lang2 -and $jackson26) {
    Write-Host "✅ Bibliotecas Java 8 restauradas" -ForegroundColor Green
} else {
    Write-Host "❌ Bibliotecas não restauradas corretamente" -ForegroundColor Red
}

# Verificar web.xml
$webxml = Get-Content "Source\WEB-INF\web.xml" -Raw
if ($webxml -match 'version="2\.4"') {
    Write-Host "✅ web.xml Servlet 2.4" -ForegroundColor Green
} else {
    Write-Host "❌ web.xml não está em Servlet 2.4" -ForegroundColor Red
}

# Verificar aplicação
try {
    $response = Invoke-WebRequest "http://localhost:8080/webp/" -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ Aplicação respondendo" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Aplicação não responde" -ForegroundColor Red
}
```

---

## Comunicação de Rollback

### Template de Email

**Assunto**: [URGENTE] Rollback Java 21 → Java 8 Executado

**Para**: Equipe de Desenvolvimento, Stakeholders, Suporte

**Corpo**:
```
Prezados,

Foi executado rollback da migração Java 21 devido a [MOTIVO].

DETALHES:
- Data/Hora: [TIMESTAMP]
- Duração do rollback: [TEMPO]
- Sistemas afetados: WebPublication
- Status atual: [OPERACIONAL/PARCIAL/INDISPONÍVEL]

AÇÕES TOMADAS:
- Código revertido para Java 8 baseline
- Dependências restauradas
- Configurações revertidas
- Ambiente Java 8 restaurado
- [Banco de dados restaurado - se aplicável]

PRÓXIMOS PASSOS:
1. Análise de causa raiz
2. Correção dos problemas identificados
3. Novo plano de migração
4. Testes adicionais em ambiente isolado

IMPACTO:
- Usuários: [DESCRIÇÃO]
- Funcionalidades: [STATUS]
- Performance: [STATUS]

Atenciosamente,
[NOME]
Tech Lead
```

---

## Lições Aprendidas

### Documentar Após Rollback:

1. **Causa Raiz**: O que causou a necessidade de rollback?
2. **Tempo de Detecção**: Quanto tempo levou para identificar o problema?
3. **Tempo de Rollback**: Quanto tempo levou para reverter?
4. **Efetividade**: O rollback resolveu completamente o problema?
5. **Melhorias**: O que pode ser melhorado no processo?

### Template de Post-Mortem:
```markdown
# Post-Mortem: Rollback Migração Java 21

## Resumo
- **Data**: [DATA]
- **Duração do Incidente**: [TEMPO]
- **Impacto**: [DESCRIÇÃO]

## Cronologia
- [HORA] - Problema detectado
- [HORA] - Decisão de rollback
- [HORA] - Rollback iniciado
- [HORA] - Rollback concluído
- [HORA] - Serviço restaurado

## Causa Raiz
[DESCRIÇÃO DETALHADA]

## Ações Corretivas
1. [AÇÃO 1]
2. [AÇÃO 2]
3. [AÇÃO 3]

## Prevenção
1. [MELHORIA 1]
2. [MELHORIA 2]
3. [MELHORIA 3]
```

---

**Responsável pelo Plano**: Tech Lead
**Aprovado por**: [Nome/Data]
**Última Revisão**: [Data]
**Próxima Revisão**: [Data]
