<?xml version="1.0" encoding="UTF-8"?>
<classpath>
	<classpathentry kind="src" path="WEB-INF/src"/>
	<classpathentry kind="src" path="resource"/>
	<classpathentry kind="con" path="org.eclipse.jst.j2ee.internal.web.container"/>
	<classpathentry kind="con" path="org.eclipse.jst.server.core.container/org.eclipse.jst.server.tomcat.runtimeTarget/Apache Tomcat v8.0 Local">
		<attributes>
			<attribute name="owner.project.facets" value="#system#;jst.web"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER/org.eclipse.jdt.internal.debug.ui.launcher.StandardVMType/jdk8u362-b09">
		<attributes>
			<attribute name="owner.project.facets" value="java"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="output" path="WEB-INF/classes"/>
</classpath>
