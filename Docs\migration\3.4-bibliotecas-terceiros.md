# 3.4 Bibliotecas de Terceiros

## Objetivo
Atualizar iText, JAI, Guava, Gson e outras bibliotecas específicas para versões compatíveis com Java 21.

## Pré-requisitos
- Frameworks web atualizados (3.3)
- Bibliotecas core atualizadas (3.2)
- Inventário de dependências concluído (1.1)

## Tempo Estimado
1-2 semanas

## Bibliotecas de Terceiros Identificadas

### **Baseado na Análise do Código Fonte:**

#### **1. PDF Generation**
- **iText 1.3.1** - Geração de PDFs
- **itextpdf-5.3.5** - Versão mais recente do iText

#### **2. Image Processing**
- **JAI (Java Advanced Imaging)**
  - jai_core-1.1.3.jar
  - jai_codec-1.1.3.jar
  - jai_imageio-1.1.jar

#### **3. JSON Processing**
- **Gson 2.2.2** - Serialização JSON
- **Boon 0.33** - JSON processing alternativo

#### **4. Utilities**
- **Guava 18.0** - Google Core Libraries
- **Apache Commons** (várias versões)

#### **5. Compression**
- **compress-lzf-1.0.2** - Compressão LZF

#### **6. XML Processing**
- **DOM4J 1.6.1** - XML parsing
- **JDOM** (se presente)

#### **7. Search/Analytics**
- **Elasticsearch 2.4.4** - Search engine
- **Lucene** (dependência do Elasticsearch)

#### **8. Visionnaire Proprietárias**
- **PSS Framework** (analisado separadamente)
- **VComponents** (viter, vjdbc, vlog, vtask, vutil)

## Análise de Compatibilidade Detalhada

### **1. iText - ALTO RISCO**

#### **Situação Atual:**
```bash
# Versões encontradas
itext-1.3.1.jar          # Muito antiga (2005)
itextpdf-5.3.5.jar       # Versão intermediária (2014)
```

#### **Problemas de Compatibilidade:**
| Versão | Java 8 | Java 11 | Java 17 | Java 21 | Licença | Status |
|--------|---------|---------|---------|---------|---------|---------|
| iText 1.3.1 | ✅ | ❌ | ❌ | ❌ | MPL/LGPL | Incompatível |
| iText 5.x | ✅ | ⚠️ | ❌ | ❌ | AGPL | Incompatível |
| iText 7.x | ✅ | ✅ | ✅ | ✅ | AGPL | **Compatível** |
| iText 8.x | ✅ | ✅ | ✅ | ✅ | AGPL | **Compatível** |

#### **Migração Recomendada:**
```xml
<!-- Remover versões antigas -->
<!-- itext-1.3.1.jar, itextpdf-5.3.5.jar -->

<!-- Adicionar iText 8.0.2 (LTS) -->
<!-- itext-core-8.0.2.jar -->
<!-- itext-layout-8.0.2.jar -->
<!-- itext-io-8.0.2.jar -->
<!-- itext-kernel-8.0.2.jar -->
```

#### **Mudanças de API:**
```java
// ANTES (iText 5.x)
import com.itextpdf.text.Document;
import com.itextpdf.text.pdf.PdfWriter;

Document document = new Document();
PdfWriter.getInstance(document, new FileOutputStream("output.pdf"));

// DEPOIS (iText 8.x)
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;

PdfWriter writer = new PdfWriter("output.pdf");
PdfDocument pdf = new PdfDocument(writer);
Document document = new Document(pdf);
```

### **2. JAI (Java Advanced Imaging) - CRÍTICO**

#### **Situação Atual:**
```bash
jai_core-1.1.3.jar        # Descontinuado pela Oracle
jai_codec-1.1.3.jar       # Não suporta Java moderno
jai_imageio-1.1.jar       # APIs internas depreciadas
```

#### **Problemas:**
- **Descontinuado** pela Oracle em 2006
- **APIs internas** do JDK removidas
- **Incompatível** com Module System
- **Sem suporte** para Java 21

#### **Alternativas Recomendadas:**

##### **Opção A: ImageIO (Built-in)**
```java
// Migração para ImageIO nativo
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;

// Funcionalidade básica sem dependências externas
BufferedImage image = ImageIO.read(new File("input.jpg"));
ImageIO.write(image, "PNG", new File("output.png"));
```

##### **Opção B: Apache Commons Imaging**
```xml
<!-- commons-imaging-1.0.0-alpha5.jar -->
```

##### **Opção C: ImageJ**
```xml
<!-- ij-1.54f.jar -->
```

### **3. Gson - MÉDIO RISCO**

#### **Situação Atual:**
```bash
gson-2.2.2.jar            # Versão muito antiga (2012)
```

#### **Compatibilidade:**
| Versão | Java 8 | Java 11 | Java 17 | Java 21 | Status |
|--------|---------|---------|---------|---------|---------|
| Gson 2.2.x | ✅ | ❌ | ❌ | ❌ | Incompatível |
| Gson 2.8.x | ✅ | ✅ | ❌ | ❌ | Incompatível |
| Gson 2.10.x | ✅ | ✅ | ✅ | ✅ | **Compatível** |

#### **Migração:**
```bash
# Remover versão antiga
del "Source\WEB-INF\lib\gson-2.2.2.jar"

# Adicionar versão compatível
copy "temp\new-libraries\gson-2.10.1.jar" "Source\WEB-INF\lib\"
```

### **4. Guava - BAIXO RISCO**

#### **Situação Atual:**
```bash
guava-18.0.jar            # Versão intermediária (2014)
```

#### **Compatibilidade:**
| Versão | Java 8 | Java 11 | Java 17 | Java 21 | Status |
|--------|---------|---------|---------|---------|---------|
| Guava 18.x | ✅ | ❌ | ❌ | ❌ | Incompatível |
| Guava 30.x | ✅ | ✅ | ✅ | ❌ | Incompatível |
| Guava 32.x | ✅ | ✅ | ✅ | ✅ | **Compatível** |

#### **Migração:**
```bash
# Atualizar para versão compatível
del "Source\WEB-INF\lib\guava-18.0.jar"
copy "temp\new-libraries\guava-32.1.3-jre.jar" "Source\WEB-INF\lib\"
```

### **5. DOM4J - BAIXO RISCO**

#### **Situação Atual:**
```bash
dom4j-1.6.1.jar           # Versão estável (2005)
```

#### **Compatibilidade:**
| Versão | Java 8 | Java 11 | Java 17 | Java 21 | Status |
|--------|---------|---------|---------|---------|---------|
| DOM4J 1.6.x | ✅ | ✅ | ⚠️ | ⚠️ | Funciona com warnings |
| DOM4J 2.1.x | ✅ | ✅ | ✅ | ✅ | **Compatível** |

#### **Migração (Opcional):**
```bash
# Atualizar para versão mais recente (opcional)
del "Source\WEB-INF\lib\dom4j-1.6.1.jar"
copy "temp\new-libraries\dom4j-2.1.4.jar" "Source\WEB-INF\lib\"
```

### **6. Elasticsearch - ALTO RISCO**

#### **Situação Atual:**
```bash
elasticsearch-2.4.4.jar   # Versão muito antiga (2016)
```

#### **Problemas:**
- **Versão 2.4.4** - Extremamente desatualizada
- **Vulnerabilidades** de segurança conhecidas
- **APIs removidas** em versões modernas
- **Incompatível** com Java 21

#### **Opções de Migração:**

##### **Opção A: Elasticsearch 8.x**
```xml
<!-- Migração complexa - APIs completamente diferentes -->
<!-- elasticsearch-java-8.9.0.jar -->
<!-- Requer reescrita de código -->
```

##### **Opção B: Apache Lucene**
```xml
<!-- lucene-core-9.7.0.jar -->
<!-- lucene-queryparser-9.7.0.jar -->
<!-- Migração mais simples -->
```

##### **Opção C: Remover Funcionalidade**
```java
// Se funcionalidade de busca não é crítica
// Implementar busca simples com SQL
```

### **7. Compress-LZF - BAIXO RISCO**

#### **Situação Atual:**
```bash
compress-lzf-1.0.2.jar    # Biblioteca de compressão
```

#### **Compatibilidade:**
- **Funciona** com Java 21
- **Sem atualizações** necessárias
- **Alternativa**: java.util.zip (built-in)

### **8. Boon - MÉDIO RISCO**

#### **Situação Atual:**
```bash
boon-0.33.jar             # JSON processing (2015)
```

#### **Problemas:**
- **Desenvolvimento descontinuado**
- **Sem suporte** para Java moderno
- **Funcionalidade duplicada** com Gson/Jackson

#### **Migração Recomendada:**
```java
// Remover Boon e usar Jackson ou Gson
// Funcionalidade já coberta por outras bibliotecas
```

## Plano de Migração Detalhado

### **Fase 3.4.1: PDF Generation (iText)**

#### **Tempo**: 3-5 dias

```bash
# 1. Backup do código atual
mkdir "backup\itext-migration"
xcopy "Source\WEB-INF\src" "backup\itext-migration\src" /E /I

# 2. Análise de uso atual
grep -r "import com.itextpdf" Source/WEB-INF/src/
grep -r "import com.lowagie" Source/WEB-INF/src/
```

#### **Script de Migração iText:**
```powershell
# migrate_itext.ps1
$sourceDir = "Source\WEB-INF\src"
$javaFiles = Get-ChildItem -Path $sourceDir -Recurse -Include "*.java"

Write-Host "Analisando uso do iText em $($javaFiles.Count) arquivos..."

$itextUsage = @()
foreach ($file in $javaFiles) {
    $content = Get-Content $file.FullName -Raw
    
    if ($content -match "import com\.itextpdf\." -or $content -match "import com\.lowagie\.") {
        $itextUsage += [PSCustomObject]@{
            File = $file.FullName
            HasiText5 = $content -match "import com\.itextpdf\."
            HasiText1 = $content -match "import com\.lowagie\."
        }
    }
}

Write-Host "Arquivos que usam iText: $($itextUsage.Count)"
$itextUsage | Format-Table -AutoSize

if ($itextUsage.Count -gt 0) {
    Write-Host "⚠️ Migração manual necessária para iText 8.x" -ForegroundColor Yellow
    Write-Host "Consulte documentação de migração do iText" -ForegroundColor Yellow
}
```

### **Fase 3.4.2: Image Processing (JAI)**

#### **Tempo**: 2-4 dias

```bash
# 1. Identificar uso do JAI
grep -r "import javax.media.jai" Source/WEB-INF/src/
grep -r "import java.awt.image.renderable" Source/WEB-INF/src/
```

#### **Migração para ImageIO:**
```java
// ANTES (JAI)
import javax.media.jai.JAI;
import javax.media.jai.RenderedOp;

RenderedOp image = JAI.create("fileload", filename);

// DEPOIS (ImageIO)
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;

BufferedImage image = ImageIO.read(new File(filename));
```

### **Fase 3.4.3: JSON Processing**

#### **Tempo**: 1-2 dias

```bash
# 1. Remover bibliotecas antigas
del "Source\WEB-INF\lib\gson-2.2.2.jar"
del "Source\WEB-INF\lib\boon-0.33.jar"

# 2. Adicionar versões compatíveis
copy "temp\new-libraries\gson-2.10.1.jar" "Source\WEB-INF\lib\"
```

### **Fase 3.4.4: Utilities (Guava, DOM4J)**

#### **Tempo**: 1 dia

```bash
# Atualizar bibliotecas utilitárias
del "Source\WEB-INF\lib\guava-18.0.jar"
copy "temp\new-libraries\guava-32.1.3-jre.jar" "Source\WEB-INF\lib\"

# DOM4J (opcional)
del "Source\WEB-INF\lib\dom4j-1.6.1.jar"
copy "temp\new-libraries\dom4j-2.1.4.jar" "Source\WEB-INF\lib\"
```

### **Fase 3.4.5: Search (Elasticsearch)**

#### **Tempo**: 3-7 dias (dependendo da opção)

#### **Análise de Uso:**
```bash
grep -r "elasticsearch" Source/WEB-INF/src/
grep -r "org.elasticsearch" Source/WEB-INF/src/
```

#### **Opção A: Migração para Lucene**
```java
// ANTES (Elasticsearch 2.x)
import org.elasticsearch.client.Client;
import org.elasticsearch.action.search.SearchResponse;

// DEPOIS (Lucene 9.x)
import org.apache.lucene.index.DirectoryReader;
import org.apache.lucene.search.IndexSearcher;
import org.apache.lucene.search.Query;
```

## Script de Download de Bibliotecas

### **download_third_party_libs.ps1**
```powershell
# Script para download de bibliotecas de terceiros
$libraries = @{
    # PDF Generation
    "itext-core-8.0.2" = "https://repo1.maven.org/maven2/com/itextpdf/itext-core/8.0.2/itext-core-8.0.2.jar"
    "itext-layout-8.0.2" = "https://repo1.maven.org/maven2/com/itextpdf/layout/8.0.2/layout-8.0.2.jar"
    
    # JSON Processing
    "gson-2.10.1" = "https://repo1.maven.org/maven2/com/google/code/gson/gson/2.10.1/gson-2.10.1.jar"
    
    # Utilities
    "guava-32.1.3-jre" = "https://repo1.maven.org/maven2/com/google/guava/guava/32.1.3-jre/guava-32.1.3-jre.jar"
    "dom4j-2.1.4" = "https://repo1.maven.org/maven2/org/dom4j/dom4j/2.1.4/dom4j-2.1.4.jar"
    
    # Image Processing
    "commons-imaging-1.0.0-alpha5" = "https://repo1.maven.org/maven2/org/apache/commons/commons-imaging/1.0.0-alpha5/commons-imaging-1.0.0-alpha5.jar"
    
    # Search (Lucene alternative)
    "lucene-core-9.7.0" = "https://repo1.maven.org/maven2/org/apache/lucene/lucene-core/9.7.0/lucene-core-9.7.0.jar"
    "lucene-queryparser-9.7.0" = "https://repo1.maven.org/maven2/org/apache/lucene/lucene-queryparser/9.7.0/lucene-queryparser-9.7.0.jar"
}

$downloadPath = "temp\third-party-libs"
New-Item -ItemType Directory -Force -Path $downloadPath

foreach ($lib in $libraries.GetEnumerator()) {
    $fileName = "$($lib.Key).jar"
    $filePath = Join-Path $downloadPath $fileName
    
    Write-Host "Downloading $($lib.Key)..."
    try {
        Invoke-WebRequest -Uri $lib.Value -OutFile $filePath
        Write-Host "✅ Downloaded: $fileName" -ForegroundColor Green
    } catch {
        Write-Host "❌ Failed to download: $($lib.Key)" -ForegroundColor Red
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "Download completed. Files saved to: $downloadPath"
```

## Script de Atualização

### **update_third_party_libs.bat**
```batch
@echo off
echo ========================================
echo    ATUALIZAÇÃO BIBLIOTECAS TERCEIROS
echo ========================================
echo.

echo === BACKUP DAS BIBLIOTECAS ATUAIS ===
mkdir "backup\third-party-libs" 2>nul
copy "Source\WEB-INF\lib\itext*.jar" "backup\third-party-libs\" 2>nul
copy "Source\WEB-INF\lib\jai*.jar" "backup\third-party-libs\" 2>nul
copy "Source\WEB-INF\lib\gson*.jar" "backup\third-party-libs\" 2>nul
copy "Source\WEB-INF\lib\guava*.jar" "backup\third-party-libs\" 2>nul
copy "Source\WEB-INF\lib\dom4j*.jar" "backup\third-party-libs\" 2>nul
copy "Source\WEB-INF\lib\elasticsearch*.jar" "backup\third-party-libs\" 2>nul
copy "Source\WEB-INF\lib\boon*.jar" "backup\third-party-libs\" 2>nul
echo ✅ Backup criado

echo.
echo === REMOVENDO BIBLIOTECAS ANTIGAS ===
del "Source\WEB-INF\lib\itext-1.3.1.jar" 2>nul
del "Source\WEB-INF\lib\itextpdf-5.*.jar" 2>nul
del "Source\WEB-INF\lib\jai_*.jar" 2>nul
del "Source\WEB-INF\lib\gson-2.2.2.jar" 2>nul
del "Source\WEB-INF\lib\guava-18.0.jar" 2>nul
del "Source\WEB-INF\lib\elasticsearch-2.*.jar" 2>nul
del "Source\WEB-INF\lib\boon-*.jar" 2>nul
echo ✅ Bibliotecas antigas removidas

echo.
echo === INSTALANDO BIBLIOTECAS JAVA 21 ===

REM JSON Processing
if exist "temp\third-party-libs\gson-2.10.1.jar" (
    copy "temp\third-party-libs\gson-2.10.1.jar" "Source\WEB-INF\lib\"
    echo ✅ Gson 2.10.1 instalado
)

REM Utilities
if exist "temp\third-party-libs\guava-32.1.3-jre.jar" (
    copy "temp\third-party-libs\guava-32.1.3-jre.jar" "Source\WEB-INF\lib\"
    echo ✅ Guava 32.1.3 instalado
)

if exist "temp\third-party-libs\dom4j-2.1.4.jar" (
    copy "temp\third-party-libs\dom4j-2.1.4.jar" "Source\WEB-INF\lib\"
    echo ✅ DOM4J 2.1.4 instalado
)

REM Image Processing
if exist "temp\third-party-libs\commons-imaging-1.0.0-alpha5.jar" (
    copy "temp\third-party-libs\commons-imaging-1.0.0-alpha5.jar" "Source\WEB-INF\lib\"
    echo ✅ Commons Imaging instalado
)

echo.
echo === VERIFICAÇÃO ===
echo Bibliotecas atualizadas:
dir "Source\WEB-INF\lib\gson*.jar" 2>nul
dir "Source\WEB-INF\lib\guava*.jar" 2>nul
dir "Source\WEB-INF\lib\dom4j*.jar" 2>nul
dir "Source\WEB-INF\lib\commons-imaging*.jar" 2>nul

echo.
echo ⚠️ ATENÇÃO: Algumas bibliotecas requerem migração manual:
echo - iText: APIs mudaram significativamente
echo - JAI: Migrar para ImageIO ou Commons Imaging
echo - Elasticsearch: Considerar migração para Lucene
echo.
echo ✅ Atualização automática concluída!
pause
```

## Testes de Compatibilidade

### **test_third_party_libs.java**
```java
public class TestThirdPartyLibs {
    
    public static void main(String[] args) {
        System.out.println("=== TESTE DE BIBLIOTECAS DE TERCEIROS ===");
        System.out.println();
        
        // Teste Gson
        testGson();
        
        // Teste Guava
        testGuava();
        
        // Teste DOM4J
        testDOM4J();
        
        // Teste Commons Imaging
        testCommonsImaging();
    }
    
    private static void testGson() {
        try {
            Class.forName("com.google.gson.Gson");
            System.out.println("✅ Gson: Carregado com sucesso");
            
            // Teste básico
            com.google.gson.Gson gson = new com.google.gson.Gson();
            String json = gson.toJson("Teste Java 21");
            System.out.println("✅ Gson: Serialização funcionando");
        } catch (Exception e) {
            System.out.println("❌ Gson: " + e.getMessage());
        }
        System.out.println();
    }
    
    private static void testGuava() {
        try {
            Class.forName("com.google.common.collect.Lists");
            System.out.println("✅ Guava: Carregado com sucesso");
            
            // Teste básico
            java.util.List<String> list = com.google.common.collect.Lists.newArrayList("Java", "21");
            System.out.println("✅ Guava: Collections funcionando");
        } catch (Exception e) {
            System.out.println("❌ Guava: " + e.getMessage());
        }
        System.out.println();
    }
    
    private static void testDOM4J() {
        try {
            Class.forName("org.dom4j.Document");
            System.out.println("✅ DOM4J: Carregado com sucesso");
        } catch (Exception e) {
            System.out.println("❌ DOM4J: " + e.getMessage());
        }
        System.out.println();
    }
    
    private static void testCommonsImaging() {
        try {
            Class.forName("org.apache.commons.imaging.Imaging");
            System.out.println("✅ Commons Imaging: Carregado com sucesso");
        } catch (Exception e) {
            System.out.println("❌ Commons Imaging: " + e.getMessage());
        }
        System.out.println();
    }
}
```

## Checklist de Validação

### **PDF Generation:**
- [ ] iText 8.x instalado ou alternativa implementada
- [ ] Código migrado para novas APIs
- [ ] Geração de PDF testada
- [ ] Licença AGPL considerada

### **Image Processing:**
- [ ] JAI removido
- [ ] ImageIO ou Commons Imaging implementado
- [ ] Processamento de imagem testado
- [ ] Performance validada

### **JSON Processing:**
- [ ] Gson 2.10.1 instalado
- [ ] Boon removido
- [ ] Serialização/deserialização testada
- [ ] Compatibilidade com Jackson verificada

### **Utilities:**
- [ ] Guava 32.x instalado
- [ ] DOM4J 2.x instalado (opcional)
- [ ] Funcionalidades testadas
- [ ] Performance verificada

### **Search:**
- [ ] Elasticsearch 2.x removido
- [ ] Alternativa implementada (Lucene/SQL)
- [ ] Funcionalidade de busca testada
- [ ] Performance comparada

## Próximo Passo
**[3.5 Validação de Dependências](./3.5-validacao-dependencias.md)**

---
**Status**: ⏳ Pendente
**Responsável**: Desenvolvedor Senior
**Estimativa**: 1-2 semanas
