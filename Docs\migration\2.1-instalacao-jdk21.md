# 2.1 Instalação do JDK 21

## Objetivo
Instalar e configurar o OpenJDK 21 ou Oracle JDK 21 no ambiente de desenvolvimento.

## Pré-requisitos
- Análise de compatibilidade concluída
- Backup do ambiente atual
- Permissões administrativas

## Tempo Estimado
2-3 horas

## Passos Detalhados

### 1. Download do JDK 21

#### Opção A: OpenJDK 21 (Recomendado)
```bash
# Download do OpenJDK 21 LTS
# URL: https://adoptium.net/temurin/releases/
# Versão: Eclipse Temurin 21 LTS

# Para Windows x64:
# https://github.com/adoptium/temurin21-binaries/releases/download/jdk-21.0.1%2B12/OpenJDK21U-jdk_x64_windows_hotspot_21.0.1_12.msi
```

#### Opção B: Oracle JDK 21
```bash
# Download do Oracle JDK 21
# URL: https://www.oracle.com/java/technologies/downloads/#java21
# Requer licença Oracle para uso comercial
```

### 2. Verificação do Ambiente Atual

```bash
# Verificar versão atual do Java
java -version
javac -version

# Verificar JAVA_HOME atual
echo %JAVA_HOME%

# Listar todas as instalações Java
dir "C:\Program Files\Java"
dir "C:\Program Files\Eclipse Adoptium"
```

### 3. Instalação do JDK 21

#### Windows - Instalação via MSI:
```bash
# Executar o instalador MSI baixado
# Instalar em: C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\

# Ou instalação manual:
# 1. Extrair ZIP para C:\Java\jdk-21
# 2. Configurar variáveis de ambiente
```

#### Verificação da Instalação:
```bash
# Verificar se foi instalado corretamente
"C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\java.exe" -version

# Deve retornar algo como:
# openjdk version "21.0.1" 2023-10-17 LTS
# OpenJDK Runtime Environment Temurin-21.0.1+12 (build 21.0.1+12-LTS)
# OpenJDK 64-Bit Server VM Temurin-21.0.1+12 (build 21.0.1+12-LTS, mixed mode, sharing)
```

### 4. Configuração de Variáveis de Ambiente

#### Backup das Configurações Atuais:
```bash
# Salvar configurações atuais
echo %JAVA_HOME% > java_home_backup.txt
echo %PATH% > path_backup.txt
```

#### Configurar JAVA_HOME:
```bash
# Via Command Prompt (como Administrador)
setx JAVA_HOME "C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot" /M

# Via PowerShell (como Administrador)
[Environment]::SetEnvironmentVariable("JAVA_HOME", "C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot", "Machine")
```

#### Atualizar PATH:
```bash
# Remover Java 8 do PATH e adicionar Java 21
# Via System Properties > Environment Variables
# Ou via PowerShell:

$currentPath = [Environment]::GetEnvironmentVariable("PATH", "Machine")
$newPath = $currentPath -replace "C:\\Program Files\\Java\\jdk1.8.0_362\\bin;", ""
$newPath = "C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin;" + $newPath
[Environment]::SetEnvironmentVariable("PATH", $newPath, "Machine")
```

### 5. Configuração para Múltiplas Versões Java

#### Script de Alternância entre Versões:
Criar `switch_java.bat`:

```batch
@echo off
if "%1"=="8" (
    set JAVA_HOME=C:\Program Files\Java\jdk1.8.0_362
    set PATH=C:\Program Files\Java\jdk1.8.0_362\bin;%PATH%
    echo Switched to Java 8
) else if "%1"=="21" (
    set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot
    set PATH=C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin;%PATH%
    echo Switched to Java 21
) else (
    echo Usage: switch_java.bat [8|21]
)

java -version
```

### 6. Verificação da Instalação

```bash
# Abrir novo Command Prompt e verificar
java -version
javac -version
echo %JAVA_HOME%

# Verificar ferramentas do JDK
jdeps --version
jlink --version
jpackage --version

# Testar compilação simples
echo public class Test { public static void main(String[] args) { System.out.println("Java 21 OK"); } } > Test.java
javac Test.java
java Test
```

### 7. Configuração de IDEs

#### Eclipse:
```
1. Window > Preferences > Java > Installed JREs
2. Add... > Standard VM
3. JRE home: C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot
4. JRE name: OpenJDK 21
5. Set as default
```

#### IntelliJ IDEA:
```
1. File > Project Structure > SDKs
2. + > Add JDK
3. Select: C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot
4. Apply
```

### 8. Atualização do Projeto WebPublication

#### Atualizar .classpath:
```xml
<!-- Substituir em Source\.classpath -->
<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER/org.eclipse.jdt.internal.debug.ui.launcher.StandardVMType/OpenJDK-21">
    <attributes>
        <attribute name="owner.project.facets" value="java"/>
    </attributes>
</classpathentry>
```

#### Atualizar .project (se necessário):
```xml
<buildSpec>
    <buildCommand>
        <name>org.eclipse.jdt.core.javabuilder</name>
        <arguments>
        </arguments>
    </buildCommand>
</buildSpec>
<natures>
    <nature>org.eclipse.jdt.core.javanature</nature>
</natures>
```

### 9. Teste de Compatibilidade Inicial

#### Compilação de Teste:
```bash
# Navegar para o projeto
cd "d:\Visionnaire-RepGit\Visionnaire-WebPublication\Source"

# Tentar compilação com Java 21
javac -cp "WEB-INF\lib\*" -d temp_classes WEB-INF\src\com\visionnaire\webpublication\Webp.java

# Verificar erros de compilação
echo %ERRORLEVEL%
```

### 10. Configuração de Ferramentas de Build

#### Atualizar ant.properties:
```properties
# Adicionar configurações para Java 21
java.home=C:/Program Files/Eclipse Adoptium/jdk-*********-hotspot
java.version=21

# Manter configuração antiga comentada
#java.home.old=D:/java/jdk1.8.0_362
```

### 11. Troubleshooting

#### Problema: "java" não é reconhecido
```bash
# Verificar PATH
echo %PATH% | findstr Java

# Reabrir Command Prompt
# Ou reiniciar sistema
```

#### Problema: Versão errada sendo usada
```bash
# Verificar múltiplas instalações
where java
where javac

# Limpar PATH de versões antigas
```

#### Problema: Permissões
```bash
# Executar como Administrador
# Verificar permissões de escrita em Program Files
```

### 12. Validação Final

#### Checklist de Verificação:
- [ ] JDK 21 instalado corretamente
- [ ] JAVA_HOME configurado
- [ ] PATH atualizado
- [ ] Ferramentas JDK funcionando
- [ ] IDE configurada
- [ ] Projeto reconhece nova versão
- [ ] Compilação básica funciona
- [ ] Script de alternância criado

#### Teste de Funcionalidades Java 21:
```java
// Criar arquivo TestJava21Features.java
public class TestJava21Features {
    public static void main(String[] args) {
        // Text Blocks (Java 13+)
        String json = """
            {
                "name": "WebPublication",
                "version": "Java 21"
            }
            """;
        
        // Pattern Matching for instanceof (Java 16+)
        Object obj = "Hello Java 21";
        if (obj instanceof String s) {
            System.out.println("String length: " + s.length());
        }
        
        // Records (Java 14+)
        record Person(String name, int age) {}
        var person = new Person("Developer", 30);
        System.out.println(person);
        
        System.out.println("Java 21 features working!");
    }
}
```

```bash
# Compilar e executar teste
javac TestJava21Features.java
java TestJava21Features
```

## Próximo Passo
**[2.2 Atualização do Apache Tomcat](./2.2-atualizacao-tomcat.md)**

---
**Status**: ⏳ Pendente
**Responsável**: DevOps/Desenvolvedor
**Estimativa**: 2-3 horas
