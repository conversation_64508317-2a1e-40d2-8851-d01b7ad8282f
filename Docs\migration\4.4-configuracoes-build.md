# 4.4 Configurações de Build

## Objetivo
Atualizar e otimizar as configurações de build (<PERSON><PERSON>, <PERSON><PERSON>, Grad<PERSON>) para compatibilidade com Java 21 e melhor performance.

## Pré-requisitos
- Propriedades da aplicação configuradas (4.3)
- Tom<PERSON> configurado (4.2)
- Dependências atualizadas (Fase 3)

## Tempo Estimado
1-2 dias

## Sistemas de Build Identificados

### **Baseado na Análise do Projeto:**

#### **1. Apache Ant (Principal)**
- **build.xml** - Script principal de build
- **ant.properties** - Propriedades de configuração
- **Scripts auxiliares** - build.bat, deploy.bat

#### **2. <PERSON>ss<PERSON><PERSON> (Futuro)**
- **pom.xml** - Para modernização do build
- **Profiles** para diferentes ambientes

#### **3. IDE Integration**
- **Eclipse .project/.classpath**
- **IntelliJ IDEA .iml**

## Configurações Ant Atualizadas

### **1. build.xml Otimizado para Java 21**

#### **Localização**: `Source/build.xml`

```xml
<?xml version="1.0" encoding="UTF-8"?>
<project name="WebPublication" default="build" basedir=".">
    
    <description>
        WebPublication Build Script - Java 21 / Jakarta EE / Tomcat 10.1
        Optimized for performance and modern development practices
    </description>
    
    <!-- Load properties -->
    <property file="ant.properties"/>
    <property file="build.properties"/>
    
    <!-- Build timestamp and version -->
    <tstamp>
        <format property="build.timestamp" pattern="yyyy-MM-dd HH:mm:ss"/>
        <format property="build.date" pattern="yyyyMMdd"/>
        <format property="build.time" pattern="HHmmss"/>
    </tstamp>
    
    <property name="build.version" value="${app.version}.${build.date}.${build.time}"/>
    <property name="build.number" value="${build.date}${build.time}"/>
    
    <!-- Directory structure -->
    <property name="src.dir" value="WEB-INF/src"/>
    <property name="classes.dir" value="WEB-INF/classes"/>
    <property name="lib.dir" value="WEB-INF/lib"/>
    <property name="web.dir" value="."/>
    <property name="dist.dir" value="dist"/>
    <property name="temp.dir" value="temp"/>
    <property name="reports.dir" value="reports"/>
    <property name="docs.dir" value="docs"/>
    
    <!-- Compilation settings -->
    <property name="compile.debug" value="true"/>
    <property name="compile.deprecation" value="true"/>
    <property name="compile.optimize" value="false"/>
    <property name="compile.encoding" value="UTF-8"/>
    <property name="compile.source" value="21"/>
    <property name="compile.target" value="21"/>
    
    <!-- Classpath definitions -->
    <path id="compile.classpath">
        <fileset dir="${lib.dir}">
            <include name="**/*.jar"/>
        </fileset>
        <fileset dir="${tomcat.home}/lib">
            <include name="servlet-api.jar"/>
            <include name="jsp-api.jar"/>
            <include name="el-api.jar"/>
        </fileset>
    </path>
    
    <path id="runtime.classpath">
        <path refid="compile.classpath"/>
        <pathelement location="${classes.dir}"/>
    </path>
    
    <!-- Test classpath -->
    <path id="test.classpath">
        <path refid="runtime.classpath"/>
        <fileset dir="test/lib" erroronmissingdir="false">
            <include name="**/*.jar"/>
        </fileset>
    </path>
    
    <!-- Clean target -->
    <target name="clean" description="Clean all build artifacts">
        <echo message="Cleaning build directories..."/>
        <delete dir="${classes.dir}" quiet="true"/>
        <delete dir="${dist.dir}" quiet="true"/>
        <delete dir="${temp.dir}" quiet="true"/>
        <delete dir="${reports.dir}" quiet="true"/>
        <delete>
            <fileset dir="." includes="**/*.class"/>
        </delete>
        <echo message="Clean completed."/>
    </target>
    
    <!-- Initialize build -->
    <target name="init" description="Initialize build environment">
        <echo message="=== WebPublication Build - Java 21 ==="/>
        <echo message="Project: ${ant.project.name}"/>
        <echo message="Version: ${build.version}"/>
        <echo message="Build Time: ${build.timestamp}"/>
        <echo message="Java Home: ${java.home}"/>
        <echo message="Java Version: ${java.version}"/>
        <echo message="Ant Version: ${ant.version}"/>
        <echo message="Tomcat Home: ${tomcat.home}"/>
        <echo message="====================================="/>
        
        <!-- Create directories -->
        <mkdir dir="${classes.dir}"/>
        <mkdir dir="${dist.dir}"/>
        <mkdir dir="${temp.dir}"/>
        <mkdir dir="${reports.dir}"/>
        
        <!-- Verify Java 21 -->
        <condition property="java21.available">
            <contains string="${java.version}" substring="21"/>
        </condition>
        <fail unless="java21.available" message="Java 21 is required for this build"/>
        
        <!-- Verify Tomcat 10.1 -->
        <available file="${tomcat.home}/lib/servlet-api.jar" property="tomcat.available"/>
        <fail unless="tomcat.available" message="Tomcat 10.1+ is required: ${tomcat.home}"/>
    </target>
    
    <!-- Compile Java sources -->
    <target name="compile" depends="init" description="Compile Java sources">
        <echo message="Compiling Java sources with Java ${java.version}..."/>
        
        <!-- Compile main sources -->
        <javac srcdir="${src.dir}"
               destdir="${classes.dir}"
               classpathref="compile.classpath"
               debug="${compile.debug}"
               deprecation="${compile.deprecation}"
               optimize="${compile.optimize}"
               encoding="${compile.encoding}"
               source="${compile.source}"
               target="${compile.target}"
               includeantruntime="false"
               fork="true"
               memorymaximumsize="1024m">
            
            <!-- Java 21 specific compiler arguments -->
            <compilerarg value="-Xlint:unchecked"/>
            <compilerarg value="-Xlint:deprecation"/>
            <compilerarg value="-parameters"/>
            <compilerarg value="--enable-preview" if="java.enable.preview"/>
            
            <!-- Module path (if using modules) -->
            <compilerarg value="--add-modules" if="java.modules"/>
            <compilerarg value="ALL-SYSTEM" if="java.modules"/>
        </javac>
        
        <!-- Copy resources -->
        <copy todir="${classes.dir}" preservelastmodified="true">
            <fileset dir="${src.dir}">
                <exclude name="**/*.java"/>
                <include name="**/*.properties"/>
                <include name="**/*.xml"/>
                <include name="**/*.txt"/>
                <include name="**/*.sql"/>
                <include name="**/*.json"/>
            </fileset>
        </copy>
        
        <!-- Process properties files -->
        <replace dir="${classes.dir}" includes="**/*.properties">
            <replacefilter token="@build.version@" value="${build.version}"/>
            <replacefilter token="@build.timestamp@" value="${build.timestamp}"/>
            <replacefilter token="@java.version@" value="${java.version}"/>
        </replace>
        
        <echo message="Compilation completed successfully."/>
    </target>
    
    <!-- Run tests -->
    <target name="test" depends="compile" description="Run unit tests">
        <echo message="Running unit tests..."/>
        
        <!-- Compile test sources -->
        <mkdir dir="${temp.dir}/test-classes"/>
        <javac srcdir="test/src" 
               destdir="${temp.dir}/test-classes"
               classpathref="test.classpath"
               debug="true"
               source="${compile.source}"
               target="${compile.target}"
               encoding="${compile.encoding}"
               includeantruntime="false"
               erroronmissingdir="false"/>
        
        <!-- Run JUnit tests -->
        <mkdir dir="${reports.dir}/junit"/>
        <junit printsummary="yes" haltonfailure="no" haltonerror="no" fork="yes">
            <classpath>
                <path refid="test.classpath"/>
                <pathelement location="${temp.dir}/test-classes"/>
            </classpath>
            
            <formatter type="xml"/>
            <formatter type="plain" usefile="false"/>
            
            <batchtest todir="${reports.dir}/junit">
                <fileset dir="test/src" erroronmissingdir="false">
                    <include name="**/*Test.java"/>
                    <include name="**/*Tests.java"/>
                </fileset>
            </batchtest>
        </junit>
        
        <!-- Generate test report -->
        <junitreport todir="${reports.dir}/junit">
            <fileset dir="${reports.dir}/junit">
                <include name="TEST-*.xml"/>
            </fileset>
            <report format="frames" todir="${reports.dir}/junit/html"/>
        </junitreport>
        
        <echo message="Tests completed. Report: ${reports.dir}/junit/html/index.html"/>
    </target>
    
    <!-- Code quality checks -->
    <target name="quality" depends="compile" description="Run code quality checks">
        <echo message="Running code quality checks..."/>
        
        <!-- CheckStyle (if available) -->
        <mkdir dir="${reports.dir}/checkstyle"/>
        <checkstyle config="checkstyle.xml" 
                   failOnViolation="false"
                   erroronmissingdir="false">
            <fileset dir="${src.dir}" includes="**/*.java"/>
            <formatter type="xml" toFile="${reports.dir}/checkstyle/checkstyle.xml"/>
        </checkstyle>
        
        <!-- PMD (if available) -->
        <mkdir dir="${reports.dir}/pmd"/>
        <pmd shortFilenames="true" erroronmissingdir="false">
            <ruleset>basic,design,imports,unusedcode</ruleset>
            <fileset dir="${src.dir}">
                <include name="**/*.java"/>
            </fileset>
            <formatter type="xml" toFile="${reports.dir}/pmd/pmd.xml"/>
        </pmd>
        
        <!-- FindBugs/SpotBugs (if available) -->
        <mkdir dir="${reports.dir}/spotbugs"/>
        <spotbugs home="${spotbugs.home}" 
                 output="xml" 
                 outputFile="${reports.dir}/spotbugs/spotbugs.xml"
                 erroronmissingdir="false">
            <auxClasspath refid="compile.classpath"/>
            <sourcePath path="${src.dir}"/>
            <class location="${classes.dir}"/>
        </spotbugs>
        
        <echo message="Quality checks completed. Reports in: ${reports.dir}/"/>
    </target>
    
    <!-- Build WAR file -->
    <target name="war" depends="compile" description="Build WAR file">
        <echo message="Building WAR file: ${war.name}"/>
        
        <!-- Create WAR -->
        <war destfile="${dist.dir}/${war.name}" 
             webxml="WEB-INF/web.xml"
             duplicate="preserve"
             compress="true"
             level="9">
            
            <!-- Web content -->
            <fileset dir="${web.dir}">
                <exclude name="WEB-INF/src/**"/>
                <exclude name="WEB-INF/classes/**"/>
                <exclude name="test/**"/>
                <exclude name="build.xml"/>
                <exclude name="ant.properties"/>
                <exclude name="build.properties"/>
                <exclude name="temp/**"/>
                <exclude name="dist/**"/>
                <exclude name="backup/**"/>
                <exclude name="docs/migration/**"/>
                <exclude name="reports/**"/>
                <exclude name="*.bat"/>
                <exclude name="*.ps1"/>
                <exclude name="*.md"/>
                <exclude name="*.log"/>
                <exclude name="**/.git/**"/>
                <exclude name="**/.svn/**"/>
                <exclude name="**/Thumbs.db"/>
                <exclude name="**/.DS_Store"/>
            </fileset>
            
            <!-- Compiled classes -->
            <classes dir="${classes.dir}"/>
            
            <!-- Libraries (exclude provided ones) -->
            <lib dir="${lib.dir}">
                <exclude name="servlet-api*.jar"/>
                <exclude name="jsp-api*.jar"/>
                <exclude name="el-api*.jar"/>
                <exclude name="tomcat-*.jar"/>
            </lib>
            
            <!-- Manifest -->
            <manifest>
                <attribute name="Built-By" value="${user.name}"/>
                <attribute name="Built-Date" value="${build.timestamp}"/>
                <attribute name="Build-Version" value="${build.version}"/>
                <attribute name="Build-Number" value="${build.number}"/>
                <attribute name="Implementation-Title" value="WebPublication"/>
                <attribute name="Implementation-Version" value="${app.version}"/>
                <attribute name="Implementation-Vendor" value="Visionnaire Informática S.A."/>
                <attribute name="Implementation-URL" value="https://www.visionnaire.com.br"/>
                <attribute name="Java-Version" value="${java.version}"/>
                <attribute name="Java-Vendor" value="${java.vendor}"/>
                <attribute name="Servlet-Version" value="5.0"/>
                <attribute name="JSP-Version" value="3.0"/>
                <attribute name="Created-By" value="Apache Ant ${ant.version}"/>
            </manifest>
        </war>
        
        <!-- Generate checksums -->
        <checksum file="${dist.dir}/${war.name}" algorithm="MD5" fileext=".md5"/>
        <checksum file="${dist.dir}/${war.name}" algorithm="SHA-256" fileext=".sha256"/>
        
        <!-- War info -->
        <length file="${dist.dir}/${war.name}" property="war.size"/>
        <echo message="WAR file created: ${dist.dir}/${war.name}"/>
        <echo message="WAR size: ${war.size} bytes"/>
        <echo message="MD5: ${dist.dir}/${war.name}.md5"/>
        <echo message="SHA-256: ${dist.dir}/${war.name}.sha256"/>
    </target>
    
    <!-- Deploy to Tomcat -->
    <target name="deploy" depends="war" description="Deploy WAR to Tomcat">
        <echo message="Deploying to Tomcat ${tomcat.version}..."/>
        
        <!-- Stop Tomcat -->
        <exec executable="${tomcat.home}/bin/shutdown.bat" 
              spawn="false" 
              failonerror="false"
              timeout="30000"/>
        
        <!-- Wait for shutdown -->
        <sleep seconds="10"/>
        
        <!-- Backup current deployment -->
        <mkdir dir="backup/deployments"/>
        <copy file="${tomcat.home}/webapps/${war.name}" 
              todir="backup/deployments" 
              failonerror="false"/>
        
        <!-- Remove old deployment -->
        <delete dir="${tomcat.home}/webapps/${app.name}" quiet="true"/>
        <delete file="${tomcat.home}/webapps/${war.name}" quiet="true"/>
        
        <!-- Deploy new WAR -->
        <copy file="${dist.dir}/${war.name}" 
              todir="${tomcat.home}/webapps"/>
        
        <!-- Start Tomcat -->
        <exec executable="${tomcat.home}/bin/startup.bat" 
              spawn="true"/>
        
        <!-- Wait for startup -->
        <echo message="Waiting for application startup..."/>
        <sleep seconds="30"/>
        
        <!-- Verify deployment -->
        <waitfor maxwait="2" maxwaitunit="minute" checkevery="5" checkeveryunit="second">
            <http url="http://localhost:8080/${app.name}/"/>
        </waitfor>
        
        <echo message="Deployment completed successfully!"/>
        <echo message="Application URL: http://localhost:8080/${app.name}/"/>
    </target>
    
    <!-- Generate documentation -->
    <target name="docs" depends="compile" description="Generate documentation">
        <echo message="Generating documentation..."/>
        
        <!-- JavaDoc -->
        <mkdir dir="${docs.dir}/javadoc"/>
        <javadoc destdir="${docs.dir}/javadoc"
                 author="true"
                 version="true"
                 use="true"
                 windowtitle="WebPublication API Documentation"
                 doctitle="WebPublication API Documentation"
                 encoding="${compile.encoding}"
                 charset="${compile.encoding}"
                 docencoding="${compile.encoding}">
            
            <packageset dir="${src.dir}" defaultexcludes="yes">
                <include name="com/visionnaire/webpublication/**"/>
            </packageset>
            
            <classpath refid="compile.classpath"/>
            
            <doctitle><![CDATA[<h1>WebPublication API Documentation</h1>]]></doctitle>
            <bottom><![CDATA[<i>Copyright &#169; 2024 Visionnaire Informática S.A. All Rights Reserved.</i>]]></bottom>
            
            <group title="Core Packages" packages="com.visionnaire.webpublication.core*"/>
            <group title="Business Logic" packages="com.visionnaire.webpublication.business*"/>
            <group title="Web Layer" packages="com.visionnaire.webpublication.web*"/>
            <group title="Utilities" packages="com.visionnaire.webpublication.util*"/>
        </javadoc>
        
        <echo message="Documentation generated: ${docs.dir}/javadoc/index.html"/>
    </target>
    
    <!-- Package source -->
    <target name="source" description="Package source code">
        <echo message="Packaging source code..."/>
        
        <zip destfile="${dist.dir}/${app.name}-${build.version}-src.zip">
            <fileset dir=".">
                <include name="WEB-INF/src/**"/>
                <include name="*.xml"/>
                <include name="*.properties"/>
                <include name="*.md"/>
                <exclude name="temp/**"/>
                <exclude name="dist/**"/>
                <exclude name="backup/**"/>
                <exclude name="reports/**"/>
            </fileset>
        </zip>
        
        <echo message="Source package: ${dist.dir}/${app.name}-${build.version}-src.zip"/>
    </target>
    
    <!-- Main build target -->
    <target name="build" depends="clean,compile,test,quality,war" description="Complete build">
        <echo message="=== Build Summary ==="/>
        <echo message="Project: ${ant.project.name}"/>
        <echo message="Version: ${build.version}"/>
        <echo message="Build Time: ${build.timestamp}"/>
        <echo message="WAR File: ${dist.dir}/${war.name}"/>
        <echo message="Java Version: ${java.version}"/>
        <echo message="Build completed successfully!"/>
        <echo message="===================="/>
    </target>
    
    <!-- Release target -->
    <target name="release" depends="build,docs,source" description="Complete release build">
        <echo message="Release build completed!"/>
        <echo message="Artifacts:"/>
        <echo message="  - WAR: ${dist.dir}/${war.name}"/>
        <echo message="  - Source: ${dist.dir}/${app.name}-${build.version}-src.zip"/>
        <echo message="  - Docs: ${docs.dir}/javadoc/"/>
        <echo message="  - Reports: ${reports.dir}/"/>
    </target>
    
    <!-- All target -->
    <target name="all" depends="release,deploy" description="Complete build and deploy">
        <echo message="Complete build and deployment finished!"/>
    </target>
    
    <!-- Development targets -->
    <target name="quick" depends="compile,war" description="Quick build for development">
        <echo message="Quick build completed: ${dist.dir}/${war.name}"/>
    </target>
    
    <target name="quick-deploy" depends="quick,deploy" description="Quick build and deploy">
        <echo message="Quick deployment completed!"/>
    </target>
    
    <!-- Utility targets -->
    <target name="info" description="Display build environment information">
        <echo message="=== Build Environment Information ==="/>
        <echo message="Project: ${ant.project.name}"/>
        <echo message="Base Directory: ${basedir}"/>
        <echo message=""/>
        <echo message="Java Home: ${java.home}"/>
        <echo message="Java Version: ${java.version}"/>
        <echo message="Java Vendor: ${java.vendor}"/>
        <echo message=""/>
        <echo message="Ant Version: ${ant.version}"/>
        <echo message="Ant Home: ${ant.home}"/>
        <echo message=""/>
        <echo message="Tomcat Home: ${tomcat.home}"/>
        <echo message="Tomcat Version: ${tomcat.version}"/>
        <echo message=""/>
        <echo message="Source Directory: ${src.dir}"/>
        <echo message="Classes Directory: ${classes.dir}"/>
        <echo message="Library Directory: ${lib.dir}"/>
        <echo message="Distribution Directory: ${dist.dir}"/>
        <echo message=""/>
        <echo message="Compile Source: ${compile.source}"/>
        <echo message="Compile Target: ${compile.target}"/>
        <echo message="Compile Encoding: ${compile.encoding}"/>
        <echo message="Compile Debug: ${compile.debug}"/>
        <echo message="====================================="/>
    </target>
    
    <target name="validate" description="Validate build configuration">
        <echo message="Validating build configuration..."/>
        
        <!-- Check Java version -->
        <condition property="java.version.ok">
            <contains string="${java.version}" substring="21"/>
        </condition>
        <fail unless="java.version.ok" message="Java 21 is required"/>
        
        <!-- Check Tomcat -->
        <available file="${tomcat.home}" type="dir" property="tomcat.exists"/>
        <fail unless="tomcat.exists" message="Tomcat directory not found: ${tomcat.home}"/>
        
        <!-- Check source directory -->
        <available file="${src.dir}" type="dir" property="src.exists"/>
        <fail unless="src.exists" message="Source directory not found: ${src.dir}"/>
        
        <!-- Check libraries -->
        <available file="${lib.dir}" type="dir" property="lib.exists"/>
        <fail unless="lib.exists" message="Library directory not found: ${lib.dir}"/>
        
        <echo message="✅ Build configuration is valid"/>
    </target>
    
</project>
```

### **2. build.properties Atualizado**

```properties
# ========================================
# Build Configuration Properties
# Java 21 / Jakarta EE / Tomcat 10.1
# ========================================

# === APPLICATION INFO ===
app.name=webp
app.version=2.0.0
app.description=WebPublication - Sistema de Publicação Web

# === BUILD SETTINGS ===
build.compiler=modern
build.sysclasspath=ignore
build.fork=true
build.memorymaximumsize=2048m

# === JAVA SETTINGS ===
java.version.required=21
java.enable.preview=false
java.modules=false

# === COMPILATION SETTINGS ===
compile.debug=true
compile.deprecation=true
compile.optimize=false
compile.encoding=UTF-8
compile.source=21
compile.target=21
compile.includeantruntime=false

# === TOMCAT SETTINGS ===
tomcat.home=D:/java/apache-tomcat-10.1.15
tomcat.version=10.1.15
tomcat.manager.url=http://localhost:8080/manager
tomcat.manager.username=deployer
tomcat.manager.password=deploy123

# === WAR SETTINGS ===
war.name=${app.name}.war
war.compression=true
war.level=9

# === DEPLOYMENT SETTINGS ===
deploy.context=${app.name}
deploy.backup=true
deploy.verify=true
deploy.timeout=120

# === TEST SETTINGS ===
test.fork=true
test.haltonfailure=false
test.haltonerror=false
test.printsummary=true

# === QUALITY SETTINGS ===
checkstyle.config=checkstyle.xml
pmd.ruleset=basic,design,imports,unusedcode
spotbugs.home=tools/spotbugs

# === DOCUMENTATION ===
javadoc.encoding=${compile.encoding}
javadoc.charset=${compile.encoding}
javadoc.docencoding=${compile.encoding}
javadoc.author=true
javadoc.version=true
javadoc.use=true

# === PERFORMANCE ===
parallel.builds=true
parallel.threads=4
incremental.compilation=true

# === ENVIRONMENT SPECIFIC ===
# Development
dev.compile.debug=true
dev.compile.optimize=false
dev.war.compression=false

# Production
prod.compile.debug=false
prod.compile.optimize=true
prod.war.compression=true
prod.war.level=9

# === PATHS ===
src.dir=WEB-INF/src
classes.dir=WEB-INF/classes
lib.dir=WEB-INF/lib
web.dir=.
dist.dir=dist
temp.dir=temp
reports.dir=reports
docs.dir=docs
backup.dir=backup

# === FILTERS ===
source.includes=**/*.java
source.excludes=**/*Test.java,**/*Tests.java
resource.includes=**/*.properties,**/*.xml,**/*.txt,**/*.sql,**/*.json
resource.excludes=**/*.bak,**/*.tmp

# === EXTERNAL TOOLS ===
junit.jar=lib/junit-4.13.2.jar
checkstyle.jar=tools/checkstyle-10.12.4-all.jar
pmd.jar=tools/pmd-bin-6.55.0.jar
```

## Scripts de Build Auxiliares

### **build-dev.bat**
```batch
@echo off
echo ========================================
echo    WEBPUBLICATION - BUILD DESENVOLVIMENTO
echo ========================================
echo.

set ANT_OPTS=-Xmx2048m -XX:+UseG1GC

echo === CONFIGURAÇÕES ===
echo Java Version: %JAVA_VERSION%
echo Ant Opts: %ANT_OPTS%
echo.

echo === BUILD RÁPIDO ===
ant quick-deploy

if %errorlevel% equ 0 (
    echo.
    echo ✅ Build de desenvolvimento concluído!
    echo 🌐 Aplicação: http://localhost:8080/webp
    echo 📁 WAR: dist\webp.war
) else (
    echo.
    echo ❌ Build falhou!
    echo Verifique os logs acima para detalhes.
)

echo.
pause
```

### **build-prod.bat**
```batch
@echo off
echo ========================================
echo    WEBPUBLICATION - BUILD PRODUÇÃO
echo ========================================
echo.

set ANT_OPTS=-Xmx4096m -XX:+UseG1GC

echo === CONFIGURAÇÕES DE PRODUÇÃO ===
echo Java Version: %JAVA_VERSION%
echo Ant Opts: %ANT_OPTS%
echo.

echo === BUILD COMPLETO ===
ant -Dcompile.debug=false -Dcompile.optimize=true -Dwar.compression=true release

if %errorlevel% equ 0 (
    echo.
    echo ✅ Build de produção concluído!
    echo 📦 WAR: dist\webp.war
    echo 📚 Docs: docs\javadoc\index.html
    echo 📊 Reports: reports\
    echo 🔍 Checksums: dist\webp.war.md5, dist\webp.war.sha256
) else (
    echo.
    echo ❌ Build falhou!
    echo Verifique os logs acima para detalhes.
)

echo.
pause
```

### **validate-build.ps1**
```powershell
# Script para validar configurações de build
Write-Host "=== VALIDAÇÃO DE CONFIGURAÇÕES DE BUILD ===" -ForegroundColor Yellow

# Verificar Java 21
$javaVersion = java -version 2>&1 | Select-String "version"
if ($javaVersion -match "21") {
    Write-Host "✅ Java 21 detectado: $javaVersion" -ForegroundColor Green
} else {
    Write-Host "❌ Java 21 não encontrado: $javaVersion" -ForegroundColor Red
}

# Verificar Ant
try {
    $antVersion = ant -version 2>&1
    Write-Host "✅ Apache Ant: $antVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Apache Ant não encontrado" -ForegroundColor Red
}

# Verificar arquivos de build
$buildFiles = @(
    "Source\build.xml",
    "Source\ant.properties", 
    "Source\build.properties"
)

Write-Host ""
Write-Host "=== VERIFICANDO ARQUIVOS DE BUILD ===" -ForegroundColor Cyan
foreach ($file in $buildFiles) {
    if (Test-Path $file) {
        Write-Host "✅ $file" -ForegroundColor Green
    } else {
        Write-Host "❌ $file - FALTANDO" -ForegroundColor Red
    }
}

# Verificar Tomcat
$tomcatHome = "D:\java\apache-tomcat-10.1.15"
if (Test-Path $tomcatHome) {
    Write-Host "✅ Tomcat 10.1.15: $tomcatHome" -ForegroundColor Green
} else {
    Write-Host "❌ Tomcat não encontrado: $tomcatHome" -ForegroundColor Red
}

# Verificar estrutura de diretórios
$directories = @(
    "Source\WEB-INF\src",
    "Source\WEB-INF\lib",
    "Source\WEB-INF\classes"
)

Write-Host ""
Write-Host "=== VERIFICANDO ESTRUTURA DE DIRETÓRIOS ===" -ForegroundColor Cyan
foreach ($dir in $directories) {
    if (Test-Path $dir) {
        $fileCount = (Get-ChildItem $dir -Recurse -File).Count
        Write-Host "✅ $dir ($fileCount arquivos)" -ForegroundColor Green
    } else {
        Write-Host "❌ $dir - FALTANDO" -ForegroundColor Red
    }
}

# Teste de build
Write-Host ""
Write-Host "=== TESTE DE BUILD ===" -ForegroundColor Cyan
Set-Location "Source"
try {
    $buildResult = ant validate 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Validação de build passou" -ForegroundColor Green
    } else {
        Write-Host "❌ Validação de build falhou" -ForegroundColor Red
        Write-Host $buildResult -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Erro ao executar ant: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    Set-Location ".."
}

Write-Host ""
Write-Host "=== VALIDAÇÃO CONCLUÍDA ===" -ForegroundColor Yellow
```

## Configuração Maven (Opcional/Futuro)

### **pom.xml para Modernização**

```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    
    <modelVersion>4.0.0</modelVersion>
    
    <groupId>com.visionnaire</groupId>
    <artifactId>webpublication</artifactId>
    <version>2.0.0</version>
    <packaging>war</packaging>
    
    <name>WebPublication</name>
    <description>Sistema de Publicação Web - Java 21</description>
    <url>https://www.visionnaire.com.br</url>
    
    <organization>
        <name>Visionnaire Informática S.A.</name>
        <url>https://www.visionnaire.com.br</url>
    </organization>
    
    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        
        <!-- Versions -->
        <jakarta.servlet.version>5.0.0</jakarta.servlet.version>
        <jakarta.jsp.version>3.0.0</jakarta.jsp.version>
        <jstl.version>3.0.1</jstl.version>
        <tiles.version>3.0.8</tiles.version>
        <jackson.version>2.15.2</jackson.version>
        <log4j.version>2.20.0</log4j.version>
        <junit.version>5.10.0</junit.version>
    </properties>
    
    <dependencies>
        <!-- Jakarta EE -->
        <dependency>
            <groupId>jakarta.servlet</groupId>
            <artifactId>jakarta.servlet-api</artifactId>
            <version>${jakarta.servlet.version}</version>
            <scope>provided</scope>
        </dependency>
        
        <dependency>
            <groupId>jakarta.servlet.jsp</groupId>
            <artifactId>jakarta.servlet.jsp-api</artifactId>
            <version>${jakarta.jsp.version}</version>
            <scope>provided</scope>
        </dependency>
        
        <!-- JSTL -->
        <dependency>
            <groupId>org.glassfish.web</groupId>
            <artifactId>jakarta.servlet.jsp.jstl</artifactId>
            <version>${jstl.version}</version>
        </dependency>
        
        <!-- Apache Tiles -->
        <dependency>
            <groupId>org.apache.tiles</groupId>
            <artifactId>tiles-core</artifactId>
            <version>${tiles.version}</version>
        </dependency>
        
        <!-- Database Drivers -->
        <dependency>
            <groupId>com.oracle.database.jdbc</groupId>
            <artifactId>ojdbc11</artifactId>
            <version>21.9.0.0</version>
        </dependency>
        
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
            <version>8.1.0</version>
        </dependency>
        
        <!-- Logging -->
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
            <version>${log4j.version}</version>
        </dependency>
        
        <!-- Testing -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>
    
    <build>
        <finalName>webp</finalName>
        <sourceDirectory>WEB-INF/src</sourceDirectory>
        <outputDirectory>WEB-INF/classes</outputDirectory>
        
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <source>21</source>
                    <target>21</target>
                    <encoding>UTF-8</encoding>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
            
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>3.4.0</version>
                <configuration>
                    <warSourceDirectory>.</warSourceDirectory>
                    <webXml>WEB-INF\web.xml</webXml>
                </configuration>
            </plugin>
        </plugins>
    </build>
    
</project>
```

## Checklist de Validação

### **Configurações Ant:**
- [ ] build.xml atualizado para Java 21
- [ ] ant.properties com configurações corretas
- [ ] build.properties otimizado
- [ ] Scripts auxiliares funcionando

### **Compilação:**
- [ ] Source/target Java 21 configurado
- [ ] Encoding UTF-8 em todos os arquivos
- [ ] Classpath com bibliotecas atualizadas
- [ ] Compiler arguments para Java 21

### **Build Process:**
- [ ] Clean/compile funcionando
- [ ] WAR generation funcionando
- [ ] Deploy automático funcionando
- [ ] Testes executando

### **Quality Assurance:**
- [ ] JavaDoc generation funcionando
- [ ] Code quality checks configurados
- [ ] Test reports gerados
- [ ] Checksums gerados

### **Performance:**
- [ ] Build paralelo configurado
- [ ] Memory settings otimizados
- [ ] Incremental compilation habilitado
- [ ] Compression otimizada

## Próximo Passo
**[5.1 Migração de Servlets](./5.1-migracao-servlets.md)**

---
**Status**: ⏳ Pendente
**Responsável**: Desenvolvedor/DevOps
**Estimativa**: 1-2 dias
