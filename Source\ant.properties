pss.home=V:/Visionnaire/PSS/Dist-3.0
vcomp.lib=V:/Visionnaire/PesquisaDesenvolvimento/VComponents/lib/3.0
vcomp.lib.opt=V:/Visionnaire/PesquisaDesenvolvimento/VComponents/lib/3.0-opt
acss.lib=V:/Visionnaire/PesquisaDesenvolvimento/ACSS/Dist-2.0/lib

# local do tomcat
home.tomcat=D:/java/apache-tomcat-8.0.53/
#dist home
dist.home=D:/webpublication/target
#nome do arquivo de build
build.file=webp.war
#cliente para o qual ser� gerado o build
build.client=visionnaire-hom

#----------------------------------------------------------------------------#
#   Configura��es para deploy		                                         #
#----------------------------------------------------------------------------#
deploy.path=/webp
deploy.url=
deploy.username=admin
deploy.password=admtomcatextranet



# inicialiar o banco
jdbc.jar=
jdbc.drv=
jdbc.url=
jdbc.usr=
jdbc.pwd=