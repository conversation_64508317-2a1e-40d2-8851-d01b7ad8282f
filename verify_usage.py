#!/usr/bin/env python3
"""
Script para verificar se arquivos aparentemente não utilizados podem estar sendo usados
através de configurações XML, anotações, reflection, etc.
"""

import os
import re
from pathlib import Path

class UsageVerifier:
    def __init__(self, project_root):
        self.project_root = Path(project_root)
        
    def check_xml_configurations(self, java_files):
        """Verifica se classes Java são referenciadas em arquivos XML"""
        print("Verificando referências em arquivos XML...")
        
        xml_files = list(self.project_root.rglob("*.xml"))
        referenced_in_xml = set()
        
        for xml_file in xml_files:
            try:
                with open(xml_file, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    
                    for java_file in java_files:
                        class_name = java_file.stem
                        full_class_path = str(java_file.relative_to(self.project_root / "Source/WEB-INF/src")).replace('/', '.').replace('.java', '')
                        
                        # Procurar por referências completas ou apenas nome da classe
                        if (full_class_path in content or 
                            class_name in content or
                            f'"{class_name}"' in content or
                            f"'{class_name}'" in content):
                            referenced_in_xml.add(java_file)
                            print(f"  ✓ {class_name} referenciado em {xml_file.name}")
                            
            except Exception as e:
                print(f"Erro ao processar {xml_file}: {e}")
                
        return referenced_in_xml
    
    def check_jsp_usage(self, jsp_files):
        """Verifica se JSPs são referenciados em outros arquivos"""
        print("Verificando referências a JSPs...")
        
        all_files = list(self.project_root.rglob("*.java")) + list(self.project_root.rglob("*.jsp")) + list(self.project_root.rglob("*.xml"))
        referenced_jsps = set()
        
        for jsp_file in jsp_files:
            jsp_name = jsp_file.name
            jsp_path = str(jsp_file.relative_to(self.project_root))
            
            for check_file in all_files:
                if check_file == jsp_file:
                    continue
                    
                try:
                    with open(check_file, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        
                        # Procurar por diferentes formas de referência
                        if (jsp_name in content or 
                            jsp_path in content or
                            jsp_path.replace('Source/', '') in content):
                            referenced_jsps.add(jsp_file)
                            print(f"  ✓ {jsp_name} referenciado em {check_file.name}")
                            break
                            
                except Exception as e:
                    continue
                    
        return referenced_jsps
    
    def check_task_classes(self, java_files):
        """Verifica classes de task que podem ser executadas via scheduler"""
        print("Verificando classes de Task...")
        
        task_classes = set()
        
        for java_file in java_files:
            if 'task' in str(java_file).lower():
                try:
                    with open(java_file, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        
                        # Procurar por implementações de Task ou Job
                        if any(pattern in content for pattern in [
                            'implements Task', 'extends Task', 'implements Job', 
                            'extends Job', '@Scheduled', 'implements Runnable'
                        ]):
                            task_classes.add(java_file)
                            print(f"  ✓ {java_file.name} é uma classe de Task/Job")
                            
                except Exception as e:
                    continue
                    
        return task_classes
    
    def check_utility_classes(self, java_files):
        """Verifica classes utilitárias que podem ser usadas via reflection"""
        print("Verificando classes utilitárias...")
        
        utility_classes = set()
        
        for java_file in java_files:
            if 'utility' in str(java_file).lower() or 'util' in str(java_file).lower():
                try:
                    with open(java_file, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        
                        # Procurar por métodos estáticos públicos
                        if re.search(r'public\s+static\s+\w+', content):
                            utility_classes.add(java_file)
                            print(f"  ✓ {java_file.name} contém métodos estáticos públicos")
                            
                except Exception as e:
                    continue
                    
        return utility_classes
    
    def check_web_services(self, java_files):
        """Verifica web services que podem ser chamados externamente"""
        print("Verificando Web Services...")
        
        webservice_classes = set()
        
        for java_file in java_files:
            try:
                with open(java_file, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    
                    # Procurar por anotações de web service
                    if any(pattern in content for pattern in [
                        '@WebService', '@WebMethod', '@Path', '@GET', '@POST',
                        'extends HttpServlet', 'implements WebService'
                    ]):
                        webservice_classes.add(java_file)
                        print(f"  ✓ {java_file.name} é um Web Service")
                        
            except Exception as e:
                continue
                
        return webservice_classes

def main():
    # Ler arquivos do relatório anterior
    unused_java = []
    unused_jsp = []
    
    with open('unused_files_report.txt', 'r', encoding='utf-8') as f:
        content = f.read()
        
        # Extrair arquivos Java não utilizados
        java_section = False
        jsp_section = False
        
        for line in content.split('\n'):
            line = line.strip()
            
            if 'ARQUIVOS JAVA NÃO UTILIZADOS:' in line:
                java_section = True
                jsp_section = False
                continue
            elif 'ARQUIVOS JSP NÃO UTILIZADOS:' in line:
                java_section = False
                jsp_section = True
                continue
            elif line.startswith('Source/') and java_section:
                unused_java.append(Path(line))
            elif line.startswith('Source/') and jsp_section:
                unused_jsp.append(Path(line))
    
    print(f"Verificando {len(unused_java)} arquivos Java e {len(unused_jsp)} arquivos JSP...")
    
    verifier = UsageVerifier('.')
    
    # Verificar diferentes tipos de uso
    xml_referenced = verifier.check_xml_configurations(unused_java)
    jsp_referenced = verifier.check_jsp_usage(unused_jsp)
    task_classes = verifier.check_task_classes(unused_java)
    utility_classes = verifier.check_utility_classes(unused_java)
    webservice_classes = verifier.check_web_services(unused_java)
    
    # Arquivos que podem estar sendo usados
    potentially_used_java = xml_referenced | task_classes | utility_classes | webservice_classes
    potentially_used_jsp = jsp_referenced
    
    # Arquivos realmente não utilizados
    truly_unused_java = set(unused_java) - potentially_used_java
    truly_unused_jsp = set(unused_jsp) - potentially_used_jsp
    
    print("\n" + "="*80)
    print("RELATÓRIO REFINADO DE ARQUIVOS NÃO UTILIZADOS")
    print("="*80)
    
    print(f"\nResumo:")
    print(f"- Arquivos Java inicialmente não utilizados: {len(unused_java)}")
    print(f"- Arquivos Java potencialmente em uso: {len(potentially_used_java)}")
    print(f"- Arquivos Java realmente não utilizados: {len(truly_unused_java)}")
    print(f"- Arquivos JSP inicialmente não utilizados: {len(unused_jsp)}")
    print(f"- Arquivos JSP potencialmente em uso: {len(potentially_used_jsp)}")
    print(f"- Arquivos JSP realmente não utilizados: {len(truly_unused_jsp)}")
    
    # Salvar relatório refinado
    with open('refined_unused_files_report.txt', 'w', encoding='utf-8') as f:
        f.write("RELATÓRIO REFINADO DE ARQUIVOS NÃO UTILIZADOS\n")
        f.write("="*50 + "\n\n")
        
        f.write("ARQUIVOS JAVA REALMENTE NÃO UTILIZADOS:\n")
        f.write("-" * 40 + "\n")
        for java_file in sorted(truly_unused_java):
            f.write(f"{java_file}\n")
        
        f.write(f"\nARQUIVOS JSP REALMENTE NÃO UTILIZADOS:\n")
        f.write("-" * 40 + "\n")
        for jsp_file in sorted(truly_unused_jsp):
            f.write(f"{jsp_file}\n")
        
        f.write(f"\nARQUIVOS JAVA POTENCIALMENTE EM USO:\n")
        f.write("-" * 40 + "\n")
        for java_file in sorted(potentially_used_java):
            f.write(f"{java_file}\n")
        
        f.write(f"\nARQUIVOS JSP POTENCIALMENTE EM USO:\n")
        f.write("-" * 40 + "\n")
        for jsp_file in sorted(potentially_used_jsp):
            f.write(f"{jsp_file}\n")
    
    print(f"\n✅ Relatório refinado salvo em: refined_unused_files_report.txt")
    print(f"🎯 Total de arquivos realmente não utilizados: {len(truly_unused_java) + len(truly_unused_jsp)}")

if __name__ == "__main__":
    main()
