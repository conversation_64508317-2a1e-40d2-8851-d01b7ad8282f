# 4.1 Atualização do web.xml

## Objetivo
Migrar o web.xml de Servlet 2.4 para versão mais recente compatível com Java 21 e Jakarta EE.

## Pré-requisitos
- Tomcat 10+ instalado
- Bibliotecas core atualizadas
- Backup do web.xml atual

## Tempo Estimado
1-2 dias

## Análise do web.xml Atual

### Versão Atual (Servlet 2.4):
```xml
<?xml version="1.0" encoding="ISO-8859-1"?>
<web-app id="WebApp_ID" version="2.4" 
    xmlns="http://java.sun.com/xml/ns/j2ee" 
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
    xsi:schemaLocation="http://java.sun.com/xml/ns/j2ee 
    http://java.sun.com/xml/ns/j2ee/web-app_2_4.xsd">
```

### Problemas Identificados:
- Servlet API 2.4 (2003) - Muito antiga
- Namespace `javax.*` - Precisa migrar para `jakarta.*`
- Schema location desatualizado
- Falta de recursos modernos

## Passos Detalhados

### 1. Backup do web.xml Atual

```bash
# Backup de todos os web.xml
mkdir "backup\webxml-configs"
copy "Source\WEB-INF\web.xml" "backup\webxml-configs\web-original.xml"
copy "Source\dist\*\web.xml" "backup\webxml-configs\"

# Documentar configurações atuais
echo "=== WEB.XML CONFIGURATIONS BACKUP ===" > "backup\webxml-configs\README.txt"
echo "Date: %date%" >> "backup\webxml-configs\README.txt"
echo "Original Servlet Version: 2.4" >> "backup\webxml-configs\README.txt"
```

### 2. Escolha da Versão Target

#### Opções de Migração:

**Opção A: Servlet 5.0 (Jakarta EE 9)**
- Namespace: `jakarta.*`
- Tomcat: 10.0.x
- Mudança de namespace obrigatória

**Opção B: Servlet 6.0 (Jakarta EE 10)**
- Namespace: `jakarta.*`
- Tomcat: 10.1.x
- Recursos mais modernos

**Recomendação: Servlet 5.0** (menor risco, mais estável)

### 3. Novo web.xml Base

#### Template Servlet 5.0:
```xml
<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns="https://jakarta.ee/xml/ns/jakartaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="https://jakarta.ee/xml/ns/jakartaee 
         https://jakarta.ee/xml/ns/jakartaee/web-app_5_0.xsd"
         version="5.0">
    
    <display-name>WebPublication</display-name>
    <description>Visionnaire Web Publication - Jakarta EE 9</description>
    
    <!-- Configuração de encoding -->
    <request-character-encoding>UTF-8</request-character-encoding>
    <response-character-encoding>UTF-8</response-character-encoding>
    
    <!-- Session timeout em minutos -->
    <session-config>
        <session-timeout>30</session-timeout>
        <cookie-config>
            <http-only>true</http-only>
            <secure>false</secure> <!-- true em produção com HTTPS -->
        </cookie-config>
    </session-config>
```

### 4. Migração de Filtros

#### PSS Filter (Atual):
```xml
<!-- ANTES (javax) -->
<filter>
    <filter-name>PSSFilter</filter-name>
    <filter-class>com.visionnaire.PSS.servlet.PSSFilter</filter-class>
    <init-param>
        <param-name>benchmark</param-name>
        <param-value>true</param-value>
    </init-param>
</filter>
```

#### PSS Filter (Migrado):
```xml
<!-- DEPOIS (jakarta) -->
<filter>
    <filter-name>PSSFilter</filter-name>
    <filter-class>com.visionnaire.PSS.servlet.PSSFilter</filter-class>
    <init-param>
        <param-name>benchmark</param-name>
        <param-value>true</param-value>
    </init-param>
    <async-supported>true</async-supported>
</filter>

<filter-mapping>
    <filter-name>PSSFilter</filter-name>
    <url-pattern>/*</url-pattern>
    <dispatcher>REQUEST</dispatcher>
    <dispatcher>FORWARD</dispatcher>
    <dispatcher>INCLUDE</dispatcher>
    <dispatcher>ERROR</dispatcher>
</filter-mapping>
```

#### DisplayTag Filter:
```xml
<filter>
    <filter-name>ResponseOverrideFilter</filter-name>
    <filter-class>org.displaytag.filter.ResponseOverrideFilter</filter-class>
</filter>

<filter-mapping>
    <filter-name>ResponseOverrideFilter</filter-name>
    <url-pattern>*.jsp</url-pattern>
</filter-mapping>
```

### 5. Migração de Servlets

#### DWR Servlet:
```xml
<servlet>
    <servlet-name>dwr-invoker</servlet-name>
    <servlet-class>org.directwebremoting.servlet.DwrServlet</servlet-class>
    <init-param>
        <param-name>debug</param-name>
        <param-value>false</param-value>
    </init-param>
    <init-param>
        <param-name>crossDomainSessionSecurity</param-name>
        <param-value>false</param-value>
    </init-param>
    <load-on-startup>1</load-on-startup>
</servlet>

<servlet-mapping>
    <servlet-name>dwr-invoker</servlet-name>
    <url-pattern>/dwr/*</url-pattern>
</servlet-mapping>
```

#### Tiles Servlet:
```xml
<servlet>
    <servlet-name>TilesDispatchServlet</servlet-name>
    <servlet-class>org.apache.tiles.web.util.TilesDispatchServlet</servlet-class>
    <load-on-startup>2</load-on-startup>
</servlet>

<servlet-mapping>
    <servlet-name>TilesDispatchServlet</servlet-name>
    <url-pattern>*.tiles</url-pattern>
</servlet-mapping>
```

### 6. Context Parameters

```xml
<!-- Tiles Configuration -->
<context-param>
    <param-name>org.apache.tiles.impl.BasicTilesContainer.DEFINITIONS_CONFIG</param-name>
    <param-value>/WEB-INF/tiles.xml</param-value>
</context-param>

<!-- DWR Configuration -->
<context-param>
    <param-name>org.directwebremoting.extend.ServerLoadMonitor</param-name>
    <param-value>org.directwebremoting.impl.DefaultServerLoadMonitor</param-value>
</context-param>

<!-- JSF Configuration (se usado) -->
<context-param>
    <param-name>jakarta.faces.PROJECT_STAGE</param-name>
    <param-value>Development</param-value>
</context-param>
```

### 7. Listeners

```xml
<!-- Context Listener para inicialização -->
<listener>
    <listener-class>com.visionnaire.webpublication.web.listener.ApplicationContextListener</listener-class>
</listener>

<!-- Session Listener -->
<listener>
    <listener-class>com.visionnaire.webpublication.web.listener.SessionListener</listener-class>
</listener>

<!-- Tiles Listener -->
<listener>
    <listener-class>org.apache.tiles.extras.complete.CompleteAutoloadTilesListener</listener-class>
</listener>
```

### 8. Resource References

```xml
<!-- DataSource Reference -->
<resource-ref>
    <description>WebPublication DataSource</description>
    <res-ref-name>jdbc/WebPublication</res-ref-name>
    <res-type>javax.sql.DataSource</res-type>
    <res-auth>Container</res-auth>
    <res-sharing-scope>Shareable</res-sharing-scope>
</resource-ref>

<!-- Mail Session Reference -->
<resource-ref>
    <description>Mail Session</description>
    <res-ref-name>mail/Session</res-ref-name>
    <res-type>jakarta.mail.Session</res-type>
    <res-auth>Container</res-auth>
</resource-ref>
```

### 9. Security Constraints (se aplicável)

```xml
<!-- HTTPS Redirect -->
<security-constraint>
    <web-resource-collection>
        <web-resource-name>Secure Area</web-resource-name>
        <url-pattern>/security/*</url-pattern>
        <url-pattern>/restricted/*</url-pattern>
    </web-resource-collection>
    <user-data-constraint>
        <transport-guarantee>CONFIDENTIAL</transport-guarantee>
    </user-data-constraint>
</security-constraint>

<!-- Login Configuration -->
<login-config>
    <auth-method>FORM</auth-method>
    <form-login-config>
        <form-login-page>/security/login.jsp</form-login-page>
        <form-error-page>/security/login-error.jsp</form-error-page>
    </form-login-config>
</login-config>
```

### 10. JSP Configuration

```xml
<jsp-config>
    <!-- JSP Property Groups -->
    <jsp-property-group>
        <url-pattern>*.jsp</url-pattern>
        <page-encoding>UTF-8</page-encoding>
        <scripting-invalid>false</scripting-invalid>
        <include-prelude>/WEB-INF/jsp/prelude.jspf</include-prelude>
    </jsp-property-group>
    
    <!-- Tag Libraries -->
    <taglib>
        <taglib-uri>/c</taglib-uri>
        <taglib-location>/WEB-INF/tld/c.tld</taglib-location>
    </taglib>
    
    <taglib>
        <taglib-uri>/fmt</taglib-uri>
        <taglib-location>/WEB-INF/tld/fmt.tld</taglib-location>
    </taglib>
    
    <taglib>
        <taglib-uri>/fn</taglib-uri>
        <taglib-location>/WEB-INF/tld/fn.tld</taglib-location>
    </taglib>
    
    <taglib>
        <taglib-uri>/displaytag</taglib-uri>
        <taglib-location>/WEB-INF/tld/displaytag.tld</taglib-location>
    </taglib>
</jsp-config>
```

### 11. Error Pages

```xml
<!-- Error Pages -->
<error-page>
    <error-code>404</error-code>
    <location>/error/404.jsp</location>
</error-page>

<error-page>
    <error-code>500</error-code>
    <location>/error/500.jsp</location>
</error-page>

<error-page>
    <exception-type>java.lang.Exception</exception-type>
    <location>/error/general.jsp</location>
</error-page>
```

### 12. Welcome Files

```xml
<welcome-file-list>
    <welcome-file>index.jsp</welcome-file>
    <welcome-file>index.html</welcome-file>
    <welcome-file>default.jsp</welcome-file>
</welcome-file-list>
```

### 13. Script de Migração

#### migrate_webxml.ps1:
```powershell
# Script para migrar web.xml
param(
    [string]$SourceFile = "Source\WEB-INF\web.xml",
    [string]$BackupDir = "backup\webxml-configs",
    [string]$TemplateFile = "templates\web-servlet5.xml"
)

Write-Host "Migrando web.xml para Servlet 5.0..."

# Backup
$timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
$backupFile = Join-Path $BackupDir "web-$timestamp.xml"
Copy-Item $SourceFile $backupFile
Write-Host "Backup criado: $backupFile"

# Ler arquivo atual
$content = Get-Content $SourceFile -Raw

# Substituições básicas
$content = $content -replace 'xmlns="http://java\.sun\.com/xml/ns/j2ee"', 'xmlns="https://jakarta.ee/xml/ns/jakartaee"'
$content = $content -replace 'version="2\.4"', 'version="5.0"'
$content = $content -replace 'web-app_2_4\.xsd', 'web-app_5_0.xsd'

# Salvar arquivo migrado
$content | Out-File $SourceFile -Encoding UTF8

Write-Host "Migração concluída!"
Write-Host "Revise manualmente o arquivo para ajustes específicos."
```

### 14. Validação

#### Checklist de Validação:
- [ ] Namespace migrado para jakarta.*
- [ ] Versão atualizada para 5.0
- [ ] Schema location correto
- [ ] Filtros migrados
- [ ] Servlets atualizados
- [ ] Context parameters revisados
- [ ] Resource references atualizados
- [ ] JSP config atualizado
- [ ] Error pages configuradas
- [ ] Encoding UTF-8 configurado

#### Teste de Validação:
```bash
# Validar XML
xmllint --noout --schema web-app_5_0.xsd Source\WEB-INF\web.xml

# Testar deploy
# (Será testado na fase de deploy do Tomcat)
```

### 15. Troubleshooting

#### Problema: Namespace não reconhecido
```
Solução: Verificar se o Tomcat 10+ está sendo usado
```

#### Problema: Classes não encontradas
```
Solução: Verificar se as bibliotecas jakarta.* estão no classpath
```

#### Problema: Filtros não funcionam
```
Solução: Verificar se as classes de filtro foram atualizadas para jakarta.*
```

## Próximo Passo
**[4.2 Configurações do Tomcat](./4.2-configuracoes-tomcat.md)**

---
**Status**: ⏳ Pendente
**Responsável**: Desenvolvedor Senior
**Estimativa**: 1-2 dias
