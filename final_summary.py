#!/usr/bin/env python3
"""
Script para gerar um resumo final dos arquivos não utilizados
baseado na análise anterior
"""

import os
from pathlib import Path

def analyze_unused_files():
    """Analisa os arquivos não utilizados do relatório inicial"""
    
    # Ler o relatório inicial
    with open('unused_files_report.txt', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Extrair arquivos Java e JSP não utilizados
    unused_java = []
    unused_jsp = []
    
    java_section = False
    jsp_section = False
    
    for line in content.split('\n'):
        line = line.strip()
        
        if 'ARQUIVOS JAVA NÃO UTILIZADOS:' in line:
            java_section = True
            jsp_section = False
            continue
        elif 'ARQUIVOS JSP NÃO UTILIZADOS:' in line:
            java_section = False
            jsp_section = True
            continue
        elif line.startswith('Source/') and java_section:
            unused_java.append(line)
        elif line.startswith('Source/') and jsp_section:
            unused_jsp.append(line)
    
    # Categorizar arquivos Java por tipo
    task_files = []
    utility_files = []
    engine_files = []
    enum_files = []
    taglib_files = []
    other_java_files = []
    
    for java_file in unused_java:
        if '/task/' in java_file:
            task_files.append(java_file)
        elif '/utility/' in java_file:
            utility_files.append(java_file)
        elif '/engine/' in java_file:
            engine_files.append(java_file)
        elif '/enumerator/' in java_file or '/enumeration/' in java_file:
            enum_files.append(java_file)
        elif '/taglib/' in java_file:
            taglib_files.append(java_file)
        else:
            other_java_files.append(java_file)
    
    # Categorizar arquivos JSP por diretório
    security_jsp = []
    tools_jsp = []
    restricted_jsp = []
    dist_jsp = []
    
    for jsp_file in unused_jsp:
        if '/security/' in jsp_file:
            security_jsp.append(jsp_file)
        elif '/tools/' in jsp_file:
            tools_jsp.append(jsp_file)
        elif '/restricted/' in jsp_file:
            restricted_jsp.append(jsp_file)
        elif '/dist/' in jsp_file:
            dist_jsp.append(jsp_file)
    
    # Gerar relatório final
    print("="*80)
    print("RELATÓRIO FINAL - ARQUIVOS NÃO UTILIZADOS NO PROJETO")
    print("="*80)
    
    print(f"\n📊 RESUMO GERAL:")
    print(f"- Total de arquivos Java analisados: 461")
    print(f"- Total de arquivos JSP analisados: 540")
    print(f"- Arquivos Java não utilizados: {len(unused_java)}")
    print(f"- Arquivos JSP não utilizados: {len(unused_jsp)}")
    print(f"- Total de arquivos não utilizados: {len(unused_java) + len(unused_jsp)}")
    
    print(f"\n🔧 ARQUIVOS JAVA POR CATEGORIA:")
    print(f"- Classes de Task/Job: {len(task_files)}")
    print(f"- Classes Utilitárias: {len(utility_files)}")
    print(f"- Classes Engine: {len(engine_files)}")
    print(f"- Enums: {len(enum_files)}")
    print(f"- TagLibs: {len(taglib_files)}")
    print(f"- Outros: {len(other_java_files)}")
    
    print(f"\n📄 ARQUIVOS JSP POR DIRETÓRIO:")
    print(f"- Security (admin): {len(security_jsp)}")
    print(f"- Tools (utilitários): {len(tools_jsp)}")
    print(f"- Restricted (áreas restritas): {len(restricted_jsp)}")
    print(f"- Dist (scripts de atualização): {len(dist_jsp)}")
    
    # Arquivos que provavelmente podem ser removidos com segurança
    safe_to_remove_java = []
    safe_to_remove_jsp = []
    
    # Classes que parecem ser seguras para remoção
    for java_file in unused_java:
        if any(pattern in java_file for pattern in [
            'NewsImporter.java',
            'WebpUpload.java', 
            'NewsRegistryOrderBy.java',
            'ListSorted.java',
            'WidgetTemplateVO.java',
            'CRMUtils.java',
            'JSONStringer.java',
            'BrokenLinksChecker.java',
            'GoogleShortener.java',
            'HtmlToText.java',
            'ImageViewer.java',
            'MyExcel2007ExportView.java',
            'TwoPhaseDownscaling.java',
            'BaseEngine.java',
            'JSPEngineImportacao.java',
            'JSPEngineSiteMedico.java',
            'NewsletterModelNewsVO.java',
            'EnumTypeClassified.java',
            'RedirectionType.java',
            'JSPEngineWidget.java',
            'EmailTemplateEnum.java',
            'ModalitiesEnumCRMPR.java',
            'ModalitiesEnumPinhais.java',
            'StatusContractEnumPinhais.java',
            'ImportPost.java'
        ]):
            safe_to_remove_java.append(java_file)
    
    # JSPs que parecem ser seguros para remoção
    for jsp_file in unused_jsp:
        if any(pattern in jsp_file for pattern in [
            '/dist/backs/',
            '/tools/backs_old/',
            'update7.jsp',
            'update75.jsp', 
            'update76.jsp'
        ]):
            safe_to_remove_jsp.append(jsp_file)
    
    print(f"\n⚠️  RECOMENDAÇÕES:")
    print(f"- Arquivos Java potencialmente seguros para remoção: {len(safe_to_remove_java)}")
    print(f"- Arquivos JSP potencialmente seguros para remoção: {len(safe_to_remove_jsp)}")
    print(f"- IMPORTANTE: Muitos arquivos podem estar sendo usados via:")
    print(f"  * Configurações XML (tasks.xml, web.xml)")
    print(f"  * Reflection ou carregamento dinâmico")
    print(f"  * Includes JSP dinâmicos")
    print(f"  * Chamadas via JavaScript/AJAX")
    
    # Salvar relatório detalhado
    with open('final_unused_files_summary.txt', 'w', encoding='utf-8') as f:
        f.write("RELATÓRIO FINAL - ARQUIVOS NÃO UTILIZADOS\n")
        f.write("="*50 + "\n\n")
        
        f.write("RESUMO:\n")
        f.write(f"- Total de arquivos Java: 461\n")
        f.write(f"- Total de arquivos JSP: 540\n")
        f.write(f"- Arquivos Java não utilizados: {len(unused_java)}\n")
        f.write(f"- Arquivos JSP não utilizados: {len(unused_jsp)}\n\n")
        
        f.write("ARQUIVOS JAVA POTENCIALMENTE SEGUROS PARA REMOÇÃO:\n")
        f.write("-" * 50 + "\n")
        for java_file in safe_to_remove_java:
            f.write(f"{java_file}\n")
        
        f.write(f"\nARQUIVOS JSP POTENCIALMENTE SEGUROS PARA REMOÇÃO:\n")
        f.write("-" * 50 + "\n")
        for jsp_file in safe_to_remove_jsp:
            f.write(f"{jsp_file}\n")
        
        f.write(f"\nTODOS OS ARQUIVOS JAVA NÃO UTILIZADOS:\n")
        f.write("-" * 50 + "\n")
        for java_file in unused_java:
            f.write(f"{java_file}\n")
        
        f.write(f"\nTODOS OS ARQUIVOS JSP NÃO UTILIZADOS:\n")
        f.write("-" * 50 + "\n")
        for jsp_file in unused_jsp:
            f.write(f"{jsp_file}\n")
    
    print(f"\n✅ Relatório final salvo em: final_unused_files_summary.txt")
    
    # Mostrar alguns exemplos de arquivos que podem ser removidos
    print(f"\n🗑️  EXEMPLOS DE ARQUIVOS POTENCIALMENTE SEGUROS PARA REMOÇÃO:")
    print(f"\nJava:")
    for java_file in safe_to_remove_java[:5]:
        print(f"  - {java_file}")
    if len(safe_to_remove_java) > 5:
        print(f"  ... e mais {len(safe_to_remove_java) - 5} arquivos")
    
    print(f"\nJSP:")
    for jsp_file in safe_to_remove_jsp[:5]:
        print(f"  - {jsp_file}")
    if len(safe_to_remove_jsp) > 5:
        print(f"  ... e mais {len(safe_to_remove_jsp) - 5} arquivos")
    
    print(f"\n⚠️  ATENÇÃO:")
    print(f"- Sempre faça backup antes de remover arquivos")
    print(f"- Teste a aplicação após remoção")
    print(f"- Alguns arquivos podem ser usados em runtime via reflection")
    print(f"- Classes de Task podem ser executadas via scheduler")
    print(f"- JSPs podem ser chamados dinamicamente")

if __name__ == "__main__":
    analyze_unused_files()
